'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';

interface MarkdownRendererProps {
  content: string;
}

export default function MarkdownRenderer({ content }: MarkdownRendererProps) {
  return (
    <div className="prose prose-invert prose-purple max-w-none prose-headings:text-purple-300 prose-headings:font-bold prose-h1:text-2xl prose-h1:font-bold sm:prose-h1:text-3xl prose-h2:text-xl prose-h2:font-bold sm:prose-h2:text-2xl prose-h3:text-lg prose-h3:font-bold sm:prose-h3:text-xl prose-h4:text-base prose-h4:font-bold sm:prose-h4:text-lg prose-h5:text-sm prose-h5:font-bold sm:prose-h5:text-base prose-h6:text-xs prose-h6:font-bold sm:prose-h6:text-sm prose-links:text-purple-400 prose-links:hover:text-purple-300 prose-strong:text-white prose-code:text-pink-300 prose-code:bg-gray-800/50 prose-code:px-2 prose-code:py-1 prose-code:rounded">
      <ReactMarkdown>
        {content}
      </ReactMarkdown>
    </div>
  );
}