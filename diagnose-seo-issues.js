// SEO诊断脚本 - 检查可能影响Google索引的问题
const { createClient } = require('@supabase/supabase-js');
const https = require('https');
const fs = require('fs');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://free-gamesonline.com';

console.log('🔍 开始SEO诊断...\n');

// 检查环境变量
function checkEnvironmentVariables() {
  console.log('1. 检查环境变量:');
  
  if (!supabaseUrl) {
    console.log('❌ NEXT_PUBLIC_SUPABASE_URL 未设置');
    return false;
  } else {
    console.log('✅ NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl);
  }
  
  if (!supabaseAnonKey) {
    console.log('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY 未设置');
    return false;
  } else {
    console.log('✅ NEXT_PUBLIC_SUPABASE_ANON_KEY: [已设置]');
  }
  
  console.log('✅ NEXT_PUBLIC_SITE_URL:', siteUrl);
  console.log('');
  return true;
}

// 检查数据库连接和数据
async function checkDatabase() {
  console.log('2. 检查数据库连接和数据:');
  
  if (!supabaseUrl || !supabaseAnonKey) {
    console.log('❌ 跳过数据库检查 - 环境变量缺失');
    return false;
  }
  
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // 检查游戏数据
    const { data: games, error: gamesError } = await supabase
      .from('free_games_online_games')
      .select('id, title, slug, status, created_at')
      .eq('status', 'published')
      .limit(5);
    
    if (gamesError) {
      console.log('❌ 游戏数据查询失败:', gamesError.message);
      return false;
    }
    
    console.log(`✅ 找到 ${games?.length || 0} 个已发布游戏`);
    if (games && games.length > 0) {
      console.log('   示例游戏:');
      games.slice(0, 3).forEach(game => {
        console.log(`   - ${game.title} (/${game.slug})`);
      });
    }
    
    // 检查分类数据
    const { data: categories, error: categoriesError } = await supabase
      .from('free_games_online_categories')
      .select('id, name, slug')
      .limit(5);
    
    if (categoriesError) {
      console.log('❌ 分类数据查询失败:', categoriesError.message);
      return false;
    }
    
    console.log(`✅ 找到 ${categories?.length || 0} 个分类`);
    if (categories && categories.length > 0) {
      console.log('   分类列表:');
      categories.forEach(category => {
        console.log(`   - ${category.name} (/${category.slug})`);
      });
    }
    
    console.log('');
    return true;
  } catch (error) {
    console.log('❌ 数据库连接失败:', error.message);
    console.log('');
    return false;
  }
}

// 检查网站可访问性
function checkWebsiteAccessibility() {
  console.log('3. 检查网站可访问性:');
  
  return new Promise((resolve) => {
    const urls = [
      `${siteUrl}/`,
      `${siteUrl}/robots.txt`,
      `${siteUrl}/sitemap.xml`
    ];
    
    let completed = 0;
    let results = [];
    
    urls.forEach(url => {
      const request = https.get(url, (res) => {
        results.push({
          url,
          status: res.statusCode,
          success: res.statusCode >= 200 && res.statusCode < 300
        });
        
        completed++;
        if (completed === urls.length) {
          results.forEach(result => {
            const status = result.success ? '✅' : '❌';
            console.log(`${status} ${result.url} - ${result.status}`);
          });
          console.log('');
          resolve(results.every(r => r.success));
        }
      }).on('error', (err) => {
        results.push({
          url,
          status: 'ERROR',
          success: false,
          error: err.message
        });
        
        completed++;
        if (completed === urls.length) {
          results.forEach(result => {
            const status = result.success ? '✅' : '❌';
            console.log(`${status} ${result.url} - ${result.status}${result.error ? ` (${result.error})` : ''}`);
          });
          console.log('');
          resolve(results.every(r => r.success));
        }
      });
      
      request.setTimeout(10000, () => {
        request.destroy();
        results.push({
          url,
          status: 'TIMEOUT',
          success: false
        });
        
        completed++;
        if (completed === urls.length) {
          results.forEach(result => {
            const status = result.success ? '✅' : '❌';
            console.log(`${status} ${result.url} - ${result.status}`);
          });
          console.log('');
          resolve(results.every(r => r.success));
        }
      });
    });
  });
}

// 检查文件配置
function checkFileConfigurations() {
  console.log('4. 检查文件配置:');
  
  const files = [
    'robots.txt',
    'sitemap.xml',
    'next.config.js',
    'next.config.ts',
    '_routes.json'
  ];
  
  files.forEach(file => {
    const publicPath = `public/${file}`;
    const rootPath = file;
    
    if (fs.existsSync(publicPath)) {
      console.log(`✅ ${publicPath} 存在`);
    } else if (fs.existsSync(rootPath)) {
      console.log(`✅ ${rootPath} 存在`);
    } else {
      console.log(`❌ ${file} 不存在`);
    }
  });
  
  console.log('');
}

// 生成SEO建议
function generateSEORecommendations() {
  console.log('📋 SEO优化建议:');
  console.log('');
  
  console.log('1. 立即检查项目:');
  console.log('   • 确保Cloudflare Pages环境变量正确配置');
  console.log('   • 检查数据库连接是否在生产环境正常工作');
  console.log('   • 验证游戏页面是否能正常加载数据');
  console.log('');
  
  console.log('2. 重新生成sitemap:');
  console.log('   • 运行: node generate-sitemap.js');
  console.log('   • 确保sitemap包含所有游戏页面');
  console.log('   • 在Google Search Console重新提交sitemap');
  console.log('');
  
  console.log('3. 页面性能优化:');
  console.log('   • 已添加结构化数据支持');
  console.log('   • 已优化数据库查询并行处理');
  console.log('   • 已修复metadata配置问题');
  console.log('');
  
  console.log('4. 监控和测试:');
  console.log('   • 使用Google PageSpeed Insights测试页面性能');
  console.log('   • 使用Google Rich Results Test验证结构化数据');
  console.log('   • 在Google Search Console查看索引状态');
  console.log('');
  
  console.log('5. 常见索引问题解决:');
  console.log('   • 如果是新网站，Google索引需要时间（几天到几周）');
  console.log('   • 确保页面加载时间 < 3秒');
  console.log('   • 检查是否有JavaScript错误影响页面渲染');
  console.log('   • 验证robots.txt没有阻止重要页面');
  console.log('');
}

// 主函数
async function main() {
  const envCheck = checkEnvironmentVariables();
  const dbCheck = await checkDatabase();
  const webCheck = await checkWebsiteAccessibility();
  checkFileConfigurations();
  
  console.log('📊 诊断结果总结:');
  console.log(`环境变量: ${envCheck ? '✅ 正常' : '❌ 有问题'}`);
  console.log(`数据库连接: ${dbCheck ? '✅ 正常' : '❌ 有问题'}`);
  console.log(`网站可访问性: ${webCheck ? '✅ 正常' : '❌ 有问题'}`);
  console.log('');
  
  if (!envCheck || !dbCheck) {
    console.log('⚠️  发现关键问题，这可能是导致Google索引失败的主要原因！');
    console.log('');
  }
  
  generateSEORecommendations();
}

main().catch(console.error);