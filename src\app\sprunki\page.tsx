import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getGamesByCategory, getCategoryBySlug } from '@/lib/api';
import { Category } from '@/lib/supabase';

// 配置 Edge Runtime 以支持 Cloudflare Pages
export const runtime = 'edge';

// 分类页面组件
export default async function SprunkiCategoryPage({
  searchParams,
}: {
  searchParams: Promise<{ page?: string }>
}) {
  // 获取当前页码，默认为第1页
  const resolvedSearchParams = await searchParams;
  const currentPage = resolvedSearchParams?.page ? parseInt(resolvedSearchParams.page) : 1;
  const gamesPerPage = 12;
  
  // 获取分类信息
  const categorySlug = 'sprunki';
  
  try {
    // 获取分类信息
    const category = await getCategoryBySlug(categorySlug);
    
    // 如果数据库中没有分类，使用默认值
    if (!category) {
      // 显示一些默认数据
      return (
        <main className="min-h-screen bg-gradient-to-b from-purple-900/40 via-black to-black text-white">
          <div className="container mx-auto px-4 py-12">
            {/* Breadcrumb Navigation */}
            <nav className="flex items-center text-base mb-8">
              <Link href="/" className="text-white hover:text-purple-300 font-medium transition-colors">Home</Link>
              <span className="mx-2 text-white">/</span>
              <span className="text-white font-medium">Sprunki Games</span>
            </nav>
            
            {/* H1 Heading and Paragraph */}
            <div className="mb-12">
              <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-pink-400 via-purple-400 to-pink-500 text-transparent bg-clip-text drop-shadow-lg">
                Sprunki Games Collection
              </h1>
              <p className="text-lg text-gray-300 max-w-3xl">
                Discover the best Sprunki Games to play instantly online for free! Our collection features unique adventures, puzzles, and challenges all starring the beloved Sprunki character. Play directly in your browser with no downloads required.
              </p>
            </div>
            
            <div className="text-center py-12 bg-black/20 rounded-xl">
              <p className="text-gray-400 text-lg">No games found in this category yet. Please add games to the database.</p>
            </div>
          </div>
        </main>
      );
    }
    
    // 获取此分类下的游戏数据
    const { games, total, pages } = await getGamesByCategory(
      categorySlug,
      currentPage,
      gamesPerPage
    );

    return (
      <main className="min-h-screen bg-gradient-to-b from-purple-900/40 via-black to-black text-white">
        <div className="container mx-auto px-4 py-12">
          {/* Breadcrumb Navigation */}
          <nav className="flex items-center text-base mb-8">
            <Link href="/" className="text-white hover:text-purple-300 font-medium transition-colors">Home</Link>
            <span className="mx-2 text-white">/</span>
            <span className="text-white font-medium">Sprunki Games</span>
          </nav>
          
          {/* H1 Heading and Paragraph */}
          <div className="mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-pink-400 via-purple-400 to-pink-500 text-transparent bg-clip-text drop-shadow-lg">
              {category?.name || 'Sprunki Games Collection'}
            </h1>
            <p className="text-lg text-gray-300 max-w-3xl">
              {category?.description || 'Discover the best Sprunki Games to play instantly online for free! Our collection features unique adventures, puzzles, and challenges all starring the beloved Sprunki character. Play directly in your browser with no downloads required.'}
            </p>
          </div>
          
          {/* Game Cards Grid */}
          {games.length === 0 ? (
            <div className="text-center py-12 bg-black/20 rounded-xl">
              <p className="text-gray-400 text-lg">No games found in this category.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
              {games.map(game => (
                <Link href={`/sprunki/${game.slug}`} key={game.id} className="group">
                  <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/10 p-[1px] rounded-xl hover:from-purple-600/20 hover:to-pink-600/20 transition-all duration-300">
                    <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl overflow-hidden h-full">
                      <div className="relative h-48 overflow-hidden">
                        <Image
                          src={game.cover_image}
                          alt={game.title}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                      </div>
                      <div className="p-4">
                        <h3 className="text-lg font-bold group-hover:text-purple-400 transition-colors">{game.title}</h3>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
          
          {/* Pagination */}
          {pages > 1 && (
            <div className="flex justify-center gap-2">
              <Link 
                href={`/sprunki?page=${Math.max(currentPage - 1, 1)}`}
                className={`px-4 py-2 rounded-lg ${
                  currentPage === 1 
                    ? 'bg-gray-800/60 text-gray-500 cursor-not-allowed pointer-events-none' 
                    : 'bg-purple-600 hover:bg-purple-700 text-white'
                }`}
              >
                Previous
              </Link>
              
              <div className="flex gap-1">
                {renderPaginationNumbers(currentPage, pages)}
              </div>
              
              <Link 
                href={`/sprunki?page=${Math.min(currentPage + 1, pages)}`}
                className={`px-4 py-2 rounded-lg ${
                  currentPage === pages 
                    ? 'bg-gray-800/60 text-gray-500 cursor-not-allowed pointer-events-none' 
                    : 'bg-purple-600 hover:bg-purple-700 text-white'
                }`}
              >
                Next
              </Link>
            </div>
          )}
        </div>
      </main>
    );
  } catch (error) {
    console.error("Error in sprunki page:", error);
    
    // 显示错误页面
    return (
      <main className="min-h-screen bg-gradient-to-b from-purple-900/40 via-black to-black text-white">
        <div className="container mx-auto px-4 py-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-pink-400 via-purple-400 to-pink-500 text-transparent bg-clip-text drop-shadow-lg">
            Something went wrong
          </h1>
          <p className="text-lg text-gray-300 mb-8">
            Unable to load Sprunki games. Please make sure your database is properly configured.
          </p>
          <Link 
            href="/"
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg"
          >
            Return Home
          </Link>
        </div>
      </main>
    );
  }
}

// 分页控件辅助函数
function renderPaginationNumbers(currentPage: number, totalPages: number) {
  const pages = [];
  
  // 计算应显示的页码范围
  if (totalPages <= 7) {
    // 少于7页时，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // 始终显示第一页
    pages.push(1);
    
    // 计算显示范围
    let rangeStart = Math.max(2, currentPage - 2);
    let rangeEnd = Math.min(totalPages - 1, currentPage + 2);
    
    // 如果靠近开头，调整结束位置
    if (rangeStart <= 3) {
      rangeStart = 2;
      rangeEnd = Math.min(6, totalPages - 1);
    }
    
    // 如果靠近结尾，调整开始位置
    if (rangeEnd >= totalPages - 2) {
      rangeEnd = totalPages - 1;
      rangeStart = Math.max(2, totalPages - 5);
    }
    
    // 添加第一页和当前范围之间的省略号
    if (rangeStart > 2) {
      pages.push('...');
    }
    
    // 添加当前页周围的页码
    for (let i = rangeStart; i <= rangeEnd; i++) {
      pages.push(i);
    }
    
    // 添加当前范围和最后一页之间的省略号
    if (rangeEnd < totalPages - 1) {
      pages.push('...');
    }
    
    // 始终显示最后一页
    if (totalPages > 1) {
      pages.push(totalPages);
    }
  }
  
  return pages.map((page, index) => {
    if (page === '...') {
      return <span key={`ellipsis-${index}`} className="px-3 flex items-center text-gray-500">...</span>;
    }
    
    const pageNum = page as number;
    return (
      <Link
        key={pageNum}
        href={`/sprunki?page=${pageNum}`}
        className={`w-10 h-10 rounded-lg flex items-center justify-center ${
          currentPage === pageNum
            ? 'bg-gradient-to-r from-pink-600 to-purple-600 text-white'
            : 'bg-gray-800/60 hover:bg-gray-700/60 text-gray-300'
        }`}
      >
        {pageNum}
      </Link>
    );
  });
} 