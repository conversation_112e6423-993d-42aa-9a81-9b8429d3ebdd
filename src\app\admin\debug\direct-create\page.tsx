'use client';
import { useState } from 'react';

export default function DirectCreatePage() {
  const [username, setUsername] = useState('admin');
  const [password, setPassword] = useState('admin');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  
  // 直接创建用户
  const createUser = async () => {
    if (!username || !password) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/admin/debug/direct-create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
        }),
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        error: true,
        message: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Create Admin User Directly</h1>
      
      <div className="bg-gray-800 p-4 rounded mb-6">
        <h2 className="text-xl font-bold mb-2">Create User</h2>
        
        <div className="mb-4">
          <label className="block mb-1">Username</label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            className="w-full bg-gray-900 border border-gray-700 rounded p-2"
          />
        </div>
        
        <div className="mb-4">
          <label className="block mb-1">Password</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full bg-gray-900 border border-gray-700 rounded p-2"
          />
        </div>
        
        <button
          onClick={createUser}
          disabled={loading || !username || !password}
          className="bg-green-600 hover:bg-green-700 disabled:bg-green-900 text-white px-4 py-2 rounded"
        >
          Create Admin User
        </button>
      </div>
      
      {result && (
        <div className="bg-gray-800 p-4 rounded">
          <h2 className="text-xl font-bold mb-2">Result</h2>
          <pre className="bg-black/50 p-4 rounded overflow-auto max-h-60">
            {JSON.stringify(result, null, 2)}
          </pre>
          
          {result.success && (
            <div className="mt-4 p-4 bg-green-900/30 border border-green-500 rounded">
              <h3 className="font-bold text-green-400 mb-2">User Created Successfully!</h3>
              <p className="text-white mb-2">You can now login with:</p>
              <ul className="list-disc list-inside text-white">
                <li>Username: <span className="font-mono bg-black/30 px-2 py-1 rounded">{username}</span></li>
                <li>Password: <span className="font-mono bg-black/30 px-2 py-1 rounded">{password}</span></li>
              </ul>
              <div className="mt-4">
                <a href="/login" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded inline-block">
                  Go to Login Page
                </a>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 