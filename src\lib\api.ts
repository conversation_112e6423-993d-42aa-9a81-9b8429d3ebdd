import { supabase, Game, Category, Tag } from './supabase';

/**
 * 获取所有游戏分类
 */
export async function getAllCategories(): Promise<Category[]> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, returning empty categories list');
      return [];
    }

    const { data, error } = await supabase
      .from('free_games_online_categories')
      .select('*')
      .order('display_order', { ascending: true });
      
    if (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
    
    return data || [];
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

/**
 * 获取特定分类的详情
 */
export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, returning null category');
      return null;
    }

    const { data, error } = await supabase
      .from('free_games_online_categories')
      .select('*')
      .eq('slug', slug)
      .single();
      
    if (error) {
      console.error(`Error fetching category ${slug}:`, error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error(`Error fetching category ${slug}:`, error);
    return null;
  }
}

/**
 * 获取某个分类的游戏列表，支持分页
 */
export async function getGamesByCategory(
  categorySlug: string, 
  page: number = 1, 
  limit: number = 12
): Promise<{ games: Game[], total: number, pages: number }> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, returning empty games list');
      return { games: [], total: 0, pages: 0 };
    }

    // 首先获取分类ID
    const category = await getCategoryBySlug(categorySlug);
    
    if (!category) {
      return { games: [], total: 0, pages: 0 };
    }
    
    // 计算偏移量用于分页
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    
    // 获取游戏总数
    const countQuery = await supabase
      .from('free_games_online_games')
      .select('id', { count: 'exact' })
      .eq('category_id', category.id)
      .eq('status', 'published');
      
    const total = countQuery.count || 0;
    
    // 获取分页的游戏数据
    const { data: games, error } = await supabase
      .from('free_games_online_games')
      .select('*')
      .eq('category_id', category.id)
      .eq('status', 'published')
      .order('created_at', { ascending: false })
      .range(from, to);
      
    if (error) {
      console.error(`Error fetching games for category ${categorySlug}:`, error);
      return { games: [], total: 0, pages: 0 };
    }
    
    // 处理JSONB类型的screenshots字段
    const processedGames = games?.map(game => ({
      ...game,
      screenshots: game.screenshots ? JSON.parse(JSON.stringify(game.screenshots)) : []
    })) || [];
    
    return { 
      games: processedGames, 
      total, 
      pages: Math.ceil(total / limit) 
    };
  } catch (error) {
    console.error(`Error fetching games for category ${categorySlug}:`, error);
    return { games: [], total: 0, pages: 0 };
  }
}

/**
 * 获取推荐或特色游戏
 */
export async function getFeaturedGames(limit: number = 4): Promise<Game[]> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, returning empty featured games list');
      return [];
    }

    const { data, error } = await supabase
      .from('free_games_online_games')
      .select('*')
      .eq('is_featured', true)
      .eq('status', 'published')
      .limit(limit);
      
    if (error) {
      console.error('Error fetching featured games:', error);
      return [];
    }
    
    // 处理JSONB类型的screenshots字段
    return data?.map(game => ({
      ...game,
      screenshots: game.screenshots ? JSON.parse(JSON.stringify(game.screenshots)) : []
    })) || [];
  } catch (error) {
    console.error('Error fetching featured games:', error);
    return [];
  }
}

/**
 * 根据slug获取游戏详情
 */
export async function getGameBySlug(slug: string): Promise<Game | null> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, returning null game');
      return null;
    }

    const { data, error } = await supabase
      .from('free_games_online_games')
      .select(`
        *,
        free_games_online_categories:category_id (
          id, name, slug
        ),
        free_games_online_game_tags!inner (
          free_games_online_tags:tag_id (
            id, name, slug
          )
        )
      `)
      .eq('slug', slug)
      .single();
      
    if (error) {
      console.error(`Error fetching game ${slug}:`, error);
      return null;
    }
    
    if (!data) return null;
    
    // 处理JSONB类型的screenshots字段
    return {
      ...data,
      screenshots: data.screenshots ? JSON.parse(JSON.stringify(data.screenshots)) : []
    };
  } catch (error) {
    console.error(`Error fetching game ${slug}:`, error);
    return null;
  }
}

/**
 * 增加游戏播放计数
 */
export async function incrementPlayCount(gameId: string): Promise<void> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, skipping play count increment');
      return;
    }

    await supabase.rpc('increment_play_count', { game_id: gameId });
  } catch (error) {
    console.error('Error incrementing play count:', error);
  }
}

/**
 * 搜索游戏
 */
export async function searchGames(query: string, limit: number = 10): Promise<Game[]> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, returning empty search results');
      return [];
    }

    const { data, error } = await supabase
      .from('free_games_online_games')
      .select('*')
      .eq('status', 'published')
      .or(`title.ilike.%${query}%,short_description.ilike.%${query}%`)
      .limit(limit);
      
    if (error) {
      console.error('Error searching games:', error);
      return [];
    }
    
    // 处理JSONB类型的screenshots字段
    return data?.map(game => ({
      ...game,
      screenshots: game.screenshots ? JSON.parse(JSON.stringify(game.screenshots)) : []
    })) || [];
  } catch (error) {
    console.error('Error searching games:', error);
    return [];
  }
} 

/**
 * 获取热门游戏 - 基于播放次数
 */
export async function getTrendingGames(excludeGameId: string, limit: number = 4): Promise<Game[]> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, returning empty trending games list');
      return [];
    }

    const { data, error } = await supabase
      .from('free_games_online_games')
      .select('*')
      .neq('id', excludeGameId) // 排除当前游戏
      .eq('status', 'published')
      .order('play_count', { ascending: false })
      .order('created_at', { ascending: false })
      .limit(limit);
      
    if (error) {
      console.error('Error fetching trending games:', error);
      return [];
    }
    
    // 处理JSONB类型的screenshots字段
    return data?.map(game => ({
      ...game,
      screenshots: game.screenshots ? JSON.parse(JSON.stringify(game.screenshots)) : []
    })) || [];
  } catch (error) {
    console.error('Error fetching trending games:', error);
    return [];
  }
}

/**
 * 获取新游戏 - 基于创建时间
 */
export async function getNewGames(excludeGameId: string, limit: number = 4): Promise<Game[]> {
  try {
    // 检查环境变量是否可用
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available, returning empty new games list');
      return [];
    }

    const { data, error } = await supabase
      .from('free_games_online_games')
      .select('*')
      .neq('id', excludeGameId) // 排除当前游戏
      .eq('status', 'published')
      .order('created_at', { ascending: false })
      .limit(limit);
      
    if (error) {
      console.error('Error fetching new games:', error);
      return [];
    }
    
    // 处理JSONB类型的screenshots字段
    return data?.map(game => ({
      ...game,
      screenshots: game.screenshots ? JSON.parse(JSON.stringify(game.screenshots)) : []
    })) || [];
  } catch (error) {
    console.error('Error fetching new games:', error);
    return [];
  }
} 