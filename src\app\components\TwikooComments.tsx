'use client';

import { useEffect, useRef } from 'react';

interface TwikooCommentsProps {
  envId: string;
  path?: string;
  className?: string;
}

declare global {
  interface Window {
    twikoo: {
      init: (config: {
        envId: string;
        el: string | HTMLElement;
        path?: string;
        lang?: string;
      }) => void;
    };
  }
}

export default function TwikooComments({
  envId,
  path,
  className = ''
}: TwikooCommentsProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 动态加载Twikoo脚本
    const script = document.createElement('script');
    script.src = 'https://cdn.staticfile.org/twikoo/1.6.44/twikoo.all.min.js';
    script.async = true;

    script.onload = () => {
      if (window.twikoo && containerRef.current) {
        window.twikoo.init({
          envId: envId,
          el: containerRef.current,
          path: path,
          lang: 'en'
        });

      }
    };

    document.head.appendChild(script);

    return () => {
      // 清理脚本
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [envId, path]);

  return (
    <div className={`twikoo-comments ${className}`}>
      <div ref={containerRef} id="tcomment"></div>
    </div>
  );
}