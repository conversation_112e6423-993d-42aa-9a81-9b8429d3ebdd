'use client';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const [userName, setUserName] = useState<string>('Admin');
  
  // Get current user info
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const sessionCookie = document.cookie
          .split('; ')
          .find(row => row.startsWith('admin_session='));
          
        if (sessionCookie) {
          const sessionData = JSON.parse(decodeURIComponent(sessionCookie.split('=')[1]));
          if (sessionData.username) {
            setUserName(sessionData.username);
          }
        }
      } catch (error) {
        console.error('Error fetching user info:', error);
      }
    };
    
    fetchUser();
  }, []);
  
  // Handle logout
  const handleLogout = async () => {
    if (confirm('Are you sure you want to log out?')) {
      try {
        const response = await fetch('/api/admin/logout', {
          method: 'POST',
          credentials: 'include'
        });
        
        if (response.ok) {
          router.push('/login');
        }
      } catch (error) {
        console.error('Logout error:', error);
      }
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 via-black to-black text-white">
      <header className="bg-black border-b border-gray-800">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-xl font-bold bg-gradient-to-r from-pink-400 via-purple-400 to-pink-500 text-transparent bg-clip-text">
              Admin Dashboard
            </h1>
          </div>
          <nav className="flex-1 mx-8">
            <ul className="flex space-x-6">
              <li>
                <a href="/admin" className="text-gray-300 hover:text-white transition-colors">
                  Dashboard
                </a>
              </li>
              <li>
                <a href="/admin/games" className="text-gray-300 hover:text-white transition-colors">
                  Games
                </a>
              </li>
              <li>
                <a href="/admin/categories" className="text-gray-300 hover:text-white transition-colors">
                  Categories
                </a>
              </li>
              <li>
                <a href="/admin/tags" className="text-gray-300 hover:text-white transition-colors">
                  Tags
                </a>
              </li>

            </ul>
          </nav>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-400">Welcome, {userName}</span>
            <button
              onClick={handleLogout}
              className="px-3 py-1 bg-red-900/30 text-red-400 hover:bg-red-800/30 rounded-lg text-sm transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </header>
      
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
      
      <footer className="border-t border-gray-800 py-4 mt-8">
        <div className="container mx-auto px-4 text-center text-sm text-gray-500">
          Admin Dashboard &copy; {new Date().getFullYear()} Free Games Online - Authorized Personnel Only
        </div>
      </footer>
    </div>
  );
} 