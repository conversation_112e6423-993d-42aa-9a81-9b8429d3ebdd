'use client';
import { useState, useEffect } from 'react';
import { createSafeSupabaseClient } from '@/lib/supabase-client';

export default function TestDbPage() {
  const [status, setStatus] = useState('Testing...');
  const [games, setGames] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function testDatabase() {
      try {
        const supabase = createSafeSupabaseClient();
        
        if (!supabase) {
          setError('Supabase client could not be created - check environment variables');
          setStatus('❌ Failed');
          return;
        }

        // 测试游戏数据
        const { data: gamesData, error: gamesError } = await supabase
          .from('free_games_online_games')
          .select('id, title, slug, status')
          .eq('status', 'published')
          .limit(5);

        if (gamesError) {
          throw new Error(`Games query failed: ${gamesError.message}`);
        }

        // 测试分类数据
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('free_games_online_categories')
          .select('id, name, slug')
          .limit(5);

        if (categoriesError) {
          throw new Error(`Categories query failed: ${categoriesError.message}`);
        }

        setGames(gamesData || []);
        setCategories(categoriesData || []);
        setStatus('✅ Database connection successful');
        setError(null);

      } catch (err: any) {
        setError(err.message);
        setStatus('❌ Database connection failed');
      }
    }

    testDatabase();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Database Connection Test</h1>
      
      <div className="bg-gray-900 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-bold mb-4 text-purple-400">Connection Status</h2>
        <div className={`p-3 rounded ${status.includes('✅') ? 'bg-green-900/30 text-green-400' : status.includes('❌') ? 'bg-red-900/30 text-red-400' : 'bg-yellow-900/30 text-yellow-400'}`}>
          {status}
        </div>
        
        {error && (
          <div className="mt-4 p-3 bg-red-900/30 text-red-400 rounded">
            <strong>Error:</strong> {error}
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-900 p-6 rounded-lg">
          <h3 className="text-lg font-bold mb-4 text-purple-400">Games ({games.length})</h3>
          {games.length > 0 ? (
            <ul className="space-y-2">
              {games.map((game) => (
                <li key={game.id} className="text-gray-300">
                  <strong>{game.title}</strong> ({game.slug})
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-400">No games found</p>
          )}
        </div>

        <div className="bg-gray-900 p-6 rounded-lg">
          <h3 className="text-lg font-bold mb-4 text-purple-400">Categories ({categories.length})</h3>
          {categories.length > 0 ? (
            <ul className="space-y-2">
              {categories.map((category) => (
                <li key={category.id} className="text-gray-300">
                  <strong>{category.name}</strong> ({category.slug})
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-400">No categories found</p>
          )}
        </div>
      </div>
    </div>
  );
}