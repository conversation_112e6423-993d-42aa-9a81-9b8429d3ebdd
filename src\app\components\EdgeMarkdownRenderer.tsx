'use client';

import React from 'react';

interface EdgeMarkdownRendererProps {
  content: string;
}

// Simple markdown parser, compatible with Edge Runtime and server-side rendering
function parseMarkdown(content: string): string {
  if (!content) return '';

  // First escape HTML special characters to prevent XSS attacks
  function escapeHtml(text: string): string {
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }

  // Function to process lists properly
  function processLists(text: string): string {
    // Process unordered lists
    text = text.replace(/((?:^\* .*$\n?)+)/gim, (match) => {
      const items = match.trim().split('\n').map(line => {
        const content = line.replace(/^\* /, '');
        return `<li class="mb-1">${content}</li>`;
      }).join('');
      return `<ul class="list-disc ml-6 mb-4">${items}</ul>`;
    });

    // Process unordered lists with dashes
    text = text.replace(/((?:^- .*$\n?)+)/gim, (match) => {
      const items = match.trim().split('\n').map(line => {
        const content = line.replace(/^- /, '');
        return `<li class="mb-1">${content}</li>`;
      }).join('');
      return `<ul class="list-disc ml-6 mb-4">${items}</ul>`;
    });

    // Process ordered lists
    text = text.replace(/((?:^\d+\. .*$\n?)+)/gim, (match) => {
      const items = match.trim().split('\n').map(line => {
        const content = line.replace(/^\d+\. /, '');
        return `<li class="mb-1">${content}</li>`;
      }).join('');
      return `<ol class="list-decimal ml-6 mb-4">${items}</ol>`;
    });

    return text;
  }

  // Preprocess content, escape HTML special characters first
  const safeContent = escapeHtml(content);

  // Process lists first, before other formatting
  let html = processLists(safeContent);

  // Parse method that doesn't depend on document object or browser-specific APIs
  html = html
    // Images - process before links to avoid conflicts
    .replace(/!\[([^\]]*)\]\(([^\)]*)\)/gim, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg mb-4 mx-auto block" loading="lazy" />')

    // Headings
    .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold mb-3 text-purple-300">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold mb-4 text-purple-300">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mb-4 text-purple-300">$1</h1>')

    // Bold - match exactly two asterisks with content that doesn't contain asterisks
    .replace(/\*\*([^*]+)\*\*/gim, '<strong class="text-white font-bold">$1</strong>')

    // Italic - match single asterisks, but use a more specific pattern
    // Match single * that is not preceded or followed by another *
    .replace(/(^|[^*])\*([^*\n]+)\*([^*]|$)/gim, '$1<em class="text-gray-300 italic">$2</em>$3')

    // Links - don't use complex character reference decoding
    .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2" class="text-purple-400 hover:text-purple-300 underline" target="_blank" rel="noopener noreferrer">$1</a>')

    // Code blocks
    .replace(/```([^`]*)```/gim, '<pre class="bg-gray-800 p-4 rounded-lg overflow-x-auto mb-4"><code class="text-pink-300">$1</code></pre>')

    // Inline code
    .replace(/`([^`]*)`/gim, '<code class="text-pink-300 bg-gray-800/50 px-2 py-1 rounded">$1</code>')

    // Clean up extra whitespace and newlines
    .replace(/\n{3,}/gim, '\n\n') // Replace multiple newlines with double newlines
    .replace(/\n\n/gim, '</p><p class="mb-4 text-gray-300">') // Convert double newlines to paragraph breaks
    .replace(/\n/gim, '<br />'); // Convert single newlines to line breaks

  // Clean up empty paragraphs and extra whitespace
  html = html
    .replace(/<p class="mb-4 text-gray-300"><\/p>/gim, '') // Remove empty paragraphs
    .replace(/<p class="mb-4 text-gray-300">\s*<br \/>\s*<\/p>/gim, '') // Remove paragraphs with only line breaks
    .replace(/(<\/(?:ul|ol|h[1-6]|pre)>)\s*<br \/>/gim, '$1') // Remove line breaks after block elements
    .replace(/<br \/>\s*(<(?:ul|ol|h[1-6]|pre))/gim, '$1') // Remove line breaks before block elements
    .trim();

  // Wrap content in paragraphs if it doesn't start with a block element
  if (html && !html.match(/^<(?:h[1-6]|ul|ol|pre|div)/)) {
    html = '<p class="mb-4 text-gray-300">' + html + '</p>';
  }

  return html;
}

// Pure client-side component, ensuring it works well in Edge environment
export default function EdgeMarkdownRenderer({ content }: EdgeMarkdownRendererProps) {
  // Ensure Markdown is parsed during component rendering, not at module level
  const htmlContent = React.useMemo(() => parseMarkdown(content), [content]);
  
  return (
    <div 
      className="prose prose-invert max-w-none"
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
}