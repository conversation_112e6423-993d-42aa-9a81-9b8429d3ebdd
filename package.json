{"name": "my-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:cf": "node ./scripts/build-for-cloudflare.js", "build:cf-official": "next build && npx @cloudflare/next-on-pages", "start": "next start", "lint": "next lint", "sitemap": "node generate-sitemap.js", "pages:build": "npx @cloudflare/next-on-pages", "preview": "npm run pages:build && wrangler pages dev", "deploy": "npm run pages:build && wrangler pages deploy", "cf-typegen": "wrangler types --env-interface CloudflareEnv env.d.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.50.5", "@tailwindcss/typography": "^0.5.16", "bcryptjs": "^3.0.2", "dotenv": "^17.2.0", "gray-matter": "^4.0.3", "markdown-it": "^14.1.0", "next": "15.1.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "remark-parse": "^11.0.0", "unified": "^11.0.5"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.7", "@cloudflare/workers-types": "^4.20250214.0", "@eslint/eslintrc": "^3", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "vercel": "^41.1.4", "wrangler": "^3.109.1"}}