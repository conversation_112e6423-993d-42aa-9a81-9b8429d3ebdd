import { createClient } from '@supabase/supabase-js';

// 服务器端 Supabase 客户端配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// 检查环境变量是否有效
export const isValidConfig = !!(process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

// 创建 Supabase 客户端（仅在配置有效时）
export const supabaseServer = isValidConfig ? createClient(supabaseUrl, supabaseKey) : null;

// 辅助函数：检查 Supabase 是否可用
export function checkSupabaseAvailable() {
  if (!supabaseServer || !isValidConfig) {
    throw new Error('Database configuration not available');
  }
  return supabaseServer;
}