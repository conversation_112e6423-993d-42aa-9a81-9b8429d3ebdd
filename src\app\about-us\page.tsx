import React from 'react';
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "About Us - Free Games Online",
  description: "Learn about Free Games Online, your ultimate destination for instant browser games with no downloads required. Play hundreds of free games across all genres!",
  alternates: {
    canonical: 'https://free-gamesonline.com/about-us',
  },
};

export default function AboutUs() {
  return (
    <main className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Title Section */}
        <h1 className="text-5xl font-bold text-center mb-4 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
          About Us
        </h1>
        <p className="text-gray-300 text-center mb-12 max-w-4xl mx-auto">
          Welcome to Free Games Online, your ultimate destination for instant browser games that require no downloads. Discover hundreds of free games across all genres and enjoy endless entertainment at your fingertips.
        </p>

        {/* Welcome Section */}
        <div className="mb-16 text-gray-300">
          <p className="mb-6">
            Welcome to <span className="text-purple-400 font-semibold">Free Games Online</span> – your one-stop platform for playing the best browser games instantly, completely free!
          </p>
          <p className="mb-6">
            At <span className="text-purple-400 font-semibold">Free Games Online</span>, we believe that great gaming experiences should be accessible to everyone. That&apos;s why we&apos;ve created a platform where players from around the world can enjoy high-quality games without the barriers of downloads, installations, or payments. Our ever-growing collection features titles across all popular genres, from action and adventure to puzzles and music games like our popular Sprunki series.
          </p>
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Our Mission
          </h2>
          <p className="text-gray-300">
            Our mission is simple: to provide the best free online gaming experience possible. We carefully curate and host games that are fun, engaging, and accessible on any device with a browser. We&apos;re committed to removing the friction between you and your next favorite game – no lengthy downloads, no complex installations, and absolutely no hidden fees or paywalls.
          </p>
        </div>

        {/* What We Offer Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            What We Offer
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                title: "Instant Gameplay",
                description: "Play any game immediately in your browser with no downloads or installations required."
              },
              {
                title: "100% Free Games",
                description: "All games on our platform are completely free to play, with no hidden costs or paywalls."
              },
              {
                title: "Diverse Game Collection",
                description: "Explore hundreds of games across all genres, from action and adventure to puzzles and music."
              },
              {
                title: "Regular Updates",
                description: "We constantly add new games to our collection to keep the experience fresh and exciting."
              },
              {
                title: "Mobile Compatibility",
                description: "Enjoy our games on any device – desktop, tablet, or mobile phone – with responsive design."
              },
              {
                title: "Safe Gaming Environment",
                description: "All our games are family-friendly and thoroughly tested for a secure and enjoyable experience."
              }
            ].map((item, index) => (
              <div key={index} className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
                <h3 className="text-xl font-bold mb-4 text-purple-400">{item.title}</h3>
                <p className="text-gray-300">{item.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Why Choose Us
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">No Barriers to Fun</h3>
              <p className="text-gray-300">
                Jump straight into games without the hassle of downloads, accounts, or payments – just pure gaming enjoyment.
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Quality Selection</h3>
              <p className="text-gray-300">
                We handpick every game for quality and entertainment value, ensuring you&apos;ll always find something worth playing.
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Play Anywhere</h3>
              <p className="text-gray-300">
                Access our games from any device with a browser – at home, at school, at work, or on the go.
              </p>
            </div>
          </div>
        </div>

        {/* Our Story Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Our Story
          </h2>
          <p className="text-gray-300 mb-6">
            Free Games Online was born from a simple idea: gaming should be accessible to everyone. We started as a small team of passionate gamers who were frustrated with the increasing barriers to entry in the gaming world – expensive hardware, large downloads, and rising game prices.
          </p>
          <p className="text-gray-300">
            We set out to create a platform where anyone could instantly enjoy quality games without these obstacles. Today, we&apos;re proud to host a diverse collection of titles that bring joy to millions of players worldwide, and we&apos;re just getting started!
          </p>
        </div>

        {/* Contact Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Contact Us
          </h2>
          <p className="text-gray-300 mb-4">
            Have questions, suggestions, or feedback? We&apos;d love to hear from you!
          </p>
          <ul className="text-gray-300">
            <li className="mb-2">
              <span className="text-purple-400">Email:</span>{" "}
              <a href="mailto:<EMAIL>" className="hover:text-purple-400 transition-colors">
                <EMAIL>
              </a>
            </li>
            <li className="mb-2">
              <span className="text-purple-400">Game Submissions:</span>{" "}
              <a href="mailto:<EMAIL>" className="hover:text-purple-400 transition-colors">
                <EMAIL>
              </a>
            </li>
            <li>
              <span className="text-purple-400">Follow Us:</span> Stay updated with new game releases and platform features on our social media channels.
            </li>
          </ul>
        </div>
      </div>
    </main>
  );
}