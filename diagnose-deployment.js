// 部署诊断脚本 - 专门检查5xx服务器错误
const { createClient } = require('@supabase/supabase-js');
const https = require('https');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://free-gamesonline.com';

console.log('🔍 诊断5xx服务器错误...\n');

// 检查环境变量
function checkEnvironmentVariables() {
  console.log('1. 环境变量检查:');
  
  const issues = [];
  
  if (!supabaseUrl) {
    issues.push('❌ NEXT_PUBLIC_SUPABASE_URL 未设置');
  } else {
    console.log('✅ NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl);
  }
  
  if (!supabaseAnonKey) {
    issues.push('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY 未设置');
  } else {
    console.log('✅ NEXT_PUBLIC_SUPABASE_ANON_KEY: [已设置]');
  }
  
  console.log('✅ NEXT_PUBLIC_SITE_URL:', siteUrl);
  
  if (issues.length > 0) {
    console.log('\n⚠️  环境变量问题:');
    issues.forEach(issue => console.log('   ' + issue));
    console.log('   这可能导致页面在服务器端抛出错误！');
  }
  
  console.log('');
  return issues.length === 0;
}

// 测试数据库连接
async function testDatabaseConnection() {
  console.log('2. 数据库连接测试:');
  
  if (!supabaseUrl || !supabaseAnonKey) {
    console.log('❌ 跳过数据库测试 - 环境变量缺失');
    return false;
  }
  
  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // 测试基本连接
    const { data, error } = await supabase
      .from('free_games_online_games')
      .select('id, title, slug, status')
      .eq('status', 'published')
      .limit(1);
    
    if (error) {
      console.log('❌ 数据库连接失败:', error.message);
      console.log('   错误详情:', error);
      return false;
    }
    
    if (!data || data.length === 0) {
      console.log('⚠️  数据库连接成功，但没有找到已发布的游戏');
      console.log('   这可能导致游戏页面返回404');
      return false;
    }
    
    console.log('✅ 数据库连接正常');
    console.log(`   找到游戏: ${data[0].title} (/${data[0].slug})`);
    
    // 测试复杂查询（模拟游戏详情页的查询）
    const testSlug = data[0].slug;
    const { data: gameData, error: gameError } = await supabase
      .from('free_games_online_games')
      .select(`
        *,
        free_games_online_categories:category_id (
          id, name, slug
        ),
        free_games_online_game_tags!inner (
          free_games_online_tags:tag_id (
            id, name, slug
          )
        )
      `)
      .eq('slug', testSlug)
      .single();
    
    if (gameError) {
      console.log('⚠️  复杂查询失败:', gameError.message);
      console.log('   这可能导致游戏详情页出错');
      return false;
    }
    
    console.log('✅ 复杂查询测试通过');
    console.log('');
    return true;
    
  } catch (error) {
    console.log('❌ 数据库测试异常:', error.message);
    console.log('');
    return false;
  }
}

// 测试API端点
async function testAPIEndpoints() {
  console.log('3. API端点测试:');
  
  const endpoints = [
    `${siteUrl}/`,
    `${siteUrl}/api/health`, // 如果有健康检查端点
    `${siteUrl}/sprunki/`, // 分类页面
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(endpoint);
      const status = response.status;
      
      if (status >= 500) {
        console.log(`❌ ${endpoint} - ${status} (服务器错误)`);
      } else if (status >= 400) {
        console.log(`⚠️  ${endpoint} - ${status} (客户端错误)`);
      } else {
        console.log(`✅ ${endpoint} - ${status}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - 连接失败: ${error.message}`);
    }
  }
  
  console.log('');
}

// 检查Cloudflare Pages特定问题
function checkCloudflareIssues() {
  console.log('4. Cloudflare Pages 特定检查:');
  
  console.log('✅ Edge Runtime 配置: 已设置');
  console.log('✅ 错误处理: 已添加 try-catch');
  console.log('✅ 环境变量检查: 已添加');
  
  console.log('\n⚠️  常见Cloudflare Pages问题:');
  console.log('   • 环境变量未在Cloudflare Pages设置中配置');
  console.log('   • 数据库连接超时（Edge Runtime限制）');
  console.log('   • 构建时的环境变量与运行时不一致');
  console.log('   • 第三方服务（如Supabase）的网络连接问题');
  
  console.log('');
}

// 生成修复建议
function generateFixRecommendations() {
  console.log('🔧 修复建议:');
  console.log('');
  
  console.log('1. 立即检查 Cloudflare Pages 环境变量:');
  console.log('   • 登录 Cloudflare Dashboard');
  console.log('   • 进入 Pages > 你的项目 > Settings > Environment variables');
  console.log('   • 确保设置了:');
  console.log('     - NEXT_PUBLIC_SUPABASE_URL');
  console.log('     - NEXT_PUBLIC_SUPABASE_ANON_KEY');
  console.log('     - NEXT_PUBLIC_SITE_URL');
  console.log('');
  
  console.log('2. 测试单个游戏页面:');
  console.log('   • 在浏览器中直接访问一个游戏页面');
  console.log('   • 打开开发者工具查看控制台错误');
  console.log('   • 检查网络请求是否失败');
  console.log('');
  
  console.log('3. 检查Supabase连接:');
  console.log('   • 确认Supabase项目状态正常');
  console.log('   • 检查RLS（行级安全）策略是否正确');
  console.log('   • 验证API密钥是否有效');
  console.log('');
  
  console.log('4. 简化页面进行测试:');
  console.log('   • 临时移除复杂的数据库查询');
  console.log('   • 先让基本页面正常工作');
  console.log('   • 逐步添加功能');
  console.log('');
  
  console.log('5. 查看Cloudflare Pages日志:');
  console.log('   • 在Cloudflare Dashboard中查看部署日志');
  console.log('   • 查看Functions日志（如果有）');
  console.log('   • 检查是否有构建时错误');
  console.log('');
}

// 创建简化版本的游戏页面用于测试
function generateSimplifiedPage() {
  console.log('📝 建议创建简化测试页面:');
  console.log('');
  console.log('创建 src/app/test-game/page.tsx:');
  console.log(`
export default function TestGamePage() {
  return (
    <div>
      <h1>Test Game Page</h1>
      <p>如果你能看到这个页面，说明基本路由工作正常</p>
      <p>环境变量测试:</p>
      <ul>
        <li>SUPABASE_URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? '已设置' : '未设置'}</li>
        <li>SUPABASE_KEY: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '已设置' : '未设置'}</li>
      </ul>
    </div>
  );
}
  `);
  console.log('');
  console.log('然后访问: https://your-domain.com/test-game');
  console.log('如果这个页面正常显示，问题就在数据库查询部分');
  console.log('');
}

// 主函数
async function main() {
  const envOk = checkEnvironmentVariables();
  const dbOk = await testDatabaseConnection();
  await testAPIEndpoints();
  checkCloudflareIssues();
  
  console.log('📊 诊断结果:');
  console.log(`环境变量: ${envOk ? '✅ 正常' : '❌ 有问题'}`);
  console.log(`数据库连接: ${dbOk ? '✅ 正常' : '❌ 有问题'}`);
  console.log('');
  
  if (!envOk || !dbOk) {
    console.log('🚨 发现关键问题！这很可能是5xx错误的原因。');
    console.log('');
  }
  
  generateFixRecommendations();
  generateSimplifiedPage();
}

main().catch(console.error);