import React from 'react';
import Link from 'next/link';
import { Game } from '@/lib/supabase';
import GameCard from './GameCard';

interface GamesListProps {
  title: string;
  games: Game[];
  viewAllLink?: string;
  viewAllText?: string;
  emptyMessage?: string;
  cardSize?: 'small' | 'medium' | 'large';
  columns?: 2 | 3 | 4 | 6;
}

export default function GamesList({ 
  title, 
  games, 
  viewAllLink, 
  viewAllText = "查看全部", 
  emptyMessage = "暂无游戏",
  cardSize = 'medium',
  columns = 4
}: GamesListProps) {
  // 根据列数确定网格样式
  const gridClasses = {
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4",
    6: "grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6"
  };

  return (
    <div className="mb-16">
      <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-pink-400 to-purple-400 text-transparent bg-clip-text">
        {title}
      </h2>
      
      {games.length > 0 ? (
        <div className={`grid ${gridClasses[columns]} gap-4`}>
          {games.map((game) => (
            <GameCard key={game.id} game={game} size={cardSize} />
          ))}
        </div>
      ) : (
        <p className="text-gray-400 text-center py-8">{emptyMessage}</p>
      )}
      
      {viewAllLink && (
        <div className="text-center mt-6">
          <Link href={viewAllLink} className="text-purple-400 hover:text-purple-300 transition-colors">
            {viewAllText}
          </Link>
        </div>
      )}
    </div>
  );
} 