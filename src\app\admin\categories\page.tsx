'use client';

import { useState, useEffect } from 'react';
import { createSafeSupabaseClient } from '@/lib/supabase-client';

interface Category {
    id: string;
    name: string;
    slug: string;
    description: string | null;
    image_url: string | null;
    game_count: number;
    display_order: number;
    created_at: string;
    updated_at: string;
}

export default function CategoriesPage() {
    const [categories, setCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingCategory, setEditingCategory] = useState<Category | null>(null);
    const [addLoading, setAddLoading] = useState(false);
    const [editLoading, setEditLoading] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        slug: '',
        description: '',
        image_url: '',
        display_order: 0
    });
    const supabase = createSafeSupabaseClient();

    useEffect(() => {
        fetchCategories();
    }, []);

    const fetchCategories = async () => {
        try {
            setLoading(true);
            
            if (!supabase) {
                throw new Error('Database connection not available');
            }
            
            const { data, error } = await supabase
                .from('free_games_online_categories')
                .select('*')
                .order('display_order', { ascending: true });

            if (error) throw error;
            setCategories(data || []);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch categories');
        } finally {
            setLoading(false);
        }
    };

    const totalGames = categories.reduce((sum, cat) => sum + cat.game_count, 0);
    const mostPopular = categories.length > 0 
        ? categories.reduce((prev, current) => prev.game_count > current.game_count ? prev : current)
        : null;

    // 生成 slug
    const generateSlug = (name: string) => {
        return name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/(^-|-$)/g, '');
    };

    // 处理表单输入
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value,
            // 自动生成 slug
            ...(name === 'name' && { slug: generateSlug(value) })
        }));
    };

    // 添加新分类
    const handleAddCategory = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!formData.name.trim()) return;

        try {
            setAddLoading(true);
            const { data, error } = await supabase
                .from('free_games_online_categories')
                .insert([{
                    name: formData.name.trim(),
                    slug: formData.slug || generateSlug(formData.name),
                    description: formData.description.trim() || null,
                    image_url: formData.image_url.trim() || null,
                    display_order: formData.display_order || categories.length,
                    game_count: 0
                }])
                .select()
                .single();

            if (error) throw error;

            // 更新分类列表
            setCategories(prev => [...prev, data]);
            
            // 重置表单
            setFormData({
                name: '',
                slug: '',
                description: '',
                image_url: '',
                display_order: 0
            });
            setShowAddModal(false);
            setError(null);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to add category');
        } finally {
            setAddLoading(false);
        }
    };

    // 打开编辑模态框
    const handleEditCategory = (category: Category) => {
        setEditingCategory(category);
        setFormData({
            name: category.name,
            slug: category.slug,
            description: category.description || '',
            image_url: category.image_url || '',
            display_order: category.display_order
        });
        setShowEditModal(true);
        setError(null);
    };

    // 更新分类
    const handleUpdateCategory = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!formData.name.trim() || !editingCategory) return;

        try {
            setEditLoading(true);
            const { data, error } = await supabase
                .from('free_games_online_categories')
                .update({
                    name: formData.name.trim(),
                    slug: formData.slug || generateSlug(formData.name),
                    description: formData.description.trim() || null,
                    image_url: formData.image_url.trim() || null,
                    display_order: formData.display_order,
                    updated_at: new Date().toISOString()
                })
                .eq('id', editingCategory.id)
                .select()
                .single();

            if (error) throw error;

            // 更新分类列表
            setCategories(prev => prev.map(cat => 
                cat.id === editingCategory.id ? data : cat
            ));
            
            // 重置状态
            setFormData({
                name: '',
                slug: '',
                description: '',
                image_url: '',
                display_order: 0
            });
            setShowEditModal(false);
            setEditingCategory(null);
            setError(null);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to update category');
        } finally {
            setEditLoading(false);
        }
    };

    // 删除分类
    const handleDeleteCategory = async (id: string, name: string) => {
        if (!confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
            return;
        }

        try {
            const { error } = await supabase
                .from('free_games_online_categories')
                .delete()
                .eq('id', id);

            if (error) throw error;

            setCategories(prev => prev.filter(cat => cat.id !== id));
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to delete category');
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-400 via-red-400 to-pink-500 text-transparent bg-clip-text">
                        Categories Management
                    </h1>
                    <p className="text-gray-400 mt-2">Manage game categories and organize your content</p>
                </div>
                <button 
                    onClick={() => setShowAddModal(true)}
                    className="px-4 py-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white rounded-lg transition-colors"
                >
                    Add New Category
                </button>
            </div>

            {/* Categories List */}
            <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl border border-purple-600/30 overflow-hidden">
                <div className="p-6 border-b border-gray-700">
                    <h2 className="text-xl font-semibold">All Categories</h2>
                </div>

                <div className="p-6">
                    {loading ? (
                        <div className="text-center py-12 text-gray-400">
                            <div className="animate-spin w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                            <p>Loading categories...</p>
                        </div>
                    ) : error ? (
                        <div className="text-center py-12 text-red-400">
                            <p>Error: {error}</p>
                            <button 
                                onClick={fetchCategories}
                                className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                            >
                                Retry
                            </button>
                        </div>
                    ) : categories.length === 0 ? (
                        <div className="text-center py-12 text-gray-400">
                            <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                            <h3 className="text-lg font-medium mb-2">No categories found</h3>
                            <p className="text-sm">Create your first category to get started</p>
                        </div>
                    ) : (
                        <div className="grid gap-4">
                            {categories.map((category) => (
                                <div key={category.id} className="bg-gray-800/50 p-4 rounded-lg border border-gray-700 hover:border-purple-500/50 transition-colors">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-4">
                                            {category.image_url ? (
                                                <img 
                                                    src={category.image_url} 
                                                    alt={category.name}
                                                    className="w-12 h-12 rounded-lg object-cover"
                                                />
                                            ) : (
                                                <div className="w-12 h-12 bg-gradient-to-r from-orange-600 to-red-600 rounded-lg flex items-center justify-center">
                                                    <span className="text-white font-bold text-lg">
                                                        {category.name.charAt(0).toUpperCase()}
                                                    </span>
                                                </div>
                                            )}
                                            <div>
                                                <h3 className="text-lg font-semibold text-white">{category.name}</h3>
                                                <p className="text-sm text-gray-400">/{category.slug}</p>
                                                {category.description && (
                                                    <p className="text-sm text-gray-300 mt-1">{category.description}</p>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-4">
                                            <div className="text-center">
                                                <div className="text-lg font-bold text-orange-400">{category.game_count}</div>
                                                <div className="text-xs text-gray-400">Games</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="text-lg font-bold text-purple-400">{category.display_order}</div>
                                                <div className="text-xs text-gray-400">Order</div>
                                            </div>
                                            <div className="flex space-x-2">
                                                <button 
                                                    onClick={() => handleEditCategory(category)}
                                                    className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors"
                                                >
                                                    Edit
                                                </button>
                                                <button 
                                                    onClick={() => handleDeleteCategory(category.id, category.name)}
                                                    className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
                                                >
                                                    Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* Category Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30">
                    <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-600 to-red-600 rounded-lg flex items-center justify-center">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <div className="text-2xl font-bold text-orange-400">{categories.length}</div>
                            <div className="text-sm text-gray-400">Total Categories</div>
                        </div>
                    </div>
                </div>

                <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30">
                    <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <div className="text-2xl font-bold text-purple-400">{totalGames}</div>
                            <div className="text-sm text-gray-400">Games in Categories</div>
                        </div>
                    </div>
                </div>

                <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30">
                    <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg flex items-center justify-center">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        </div>
                        <div className="ml-4">
                            <div className="text-2xl font-bold text-green-400">
                                {mostPopular ? mostPopular.name : '--'}
                            </div>
                            <div className="text-sm text-gray-400">Most Popular</div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Add Category Modal */}
            {showAddModal && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
                    <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 w-full max-w-md mx-4">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-xl font-semibold">Add New Category</h3>
                            <button
                                onClick={() => setShowAddModal(false)}
                                className="text-gray-400 hover:text-white"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <form onSubmit={handleAddCategory} className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Category Name *
                                </label>
                                <input
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="Enter category name"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Slug
                                </label>
                                <input
                                    type="text"
                                    name="slug"
                                    value={formData.slug}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="Auto-generated from name"
                                />
                                <p className="text-xs text-gray-400 mt-1">URL-friendly version of the name</p>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Description
                                </label>
                                <textarea
                                    name="description"
                                    value={formData.description}
                                    onChange={handleInputChange}
                                    rows={3}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="Optional description"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Image URL
                                </label>
                                <input
                                    type="url"
                                    name="image_url"
                                    value={formData.image_url}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="https://example.com/image.jpg"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Display Order
                                </label>
                                <input
                                    type="number"
                                    name="display_order"
                                    value={formData.display_order}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="0"
                                    min="0"
                                />
                                <p className="text-xs text-gray-400 mt-1">Lower numbers appear first</p>
                            </div>

                            {error && (
                                <div className="p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-300 text-sm">
                                    {error}
                                </div>
                            )}

                            <div className="flex space-x-3 pt-4">
                                <button
                                    type="button"
                                    onClick={() => setShowAddModal(false)}
                                    className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={addLoading || !formData.name.trim()}
                                    className="flex-1 px-4 py-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {addLoading ? 'Adding...' : 'Add Category'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            {/* Edit Category Modal */}
            {showEditModal && editingCategory && (
                <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
                    <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 w-full max-w-md mx-4">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-xl font-semibold">Edit Category</h3>
                            <button
                                onClick={() => {
                                    setShowEditModal(false);
                                    setEditingCategory(null);
                                    setError(null);
                                }}
                                className="text-gray-400 hover:text-white"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <form onSubmit={handleUpdateCategory} className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Category Name *
                                </label>
                                <input
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                                    placeholder="Enter category name"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Slug
                                </label>
                                <input
                                    type="text"
                                    name="slug"
                                    value={formData.slug}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                                    placeholder="Auto-generated from name"
                                />
                                <p className="text-xs text-gray-400 mt-1">URL-friendly version of the name</p>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Description
                                </label>
                                <textarea
                                    name="description"
                                    value={formData.description}
                                    onChange={handleInputChange}
                                    rows={3}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                                    placeholder="Optional description"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Image URL
                                </label>
                                <input
                                    type="url"
                                    name="image_url"
                                    value={formData.image_url}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                                    placeholder="https://example.com/image.jpg"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-2">
                                    Display Order
                                </label>
                                <input
                                    type="number"
                                    name="display_order"
                                    value={formData.display_order}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500"
                                    placeholder="0"
                                    min="0"
                                />
                                <p className="text-xs text-gray-400 mt-1">Lower numbers appear first</p>
                            </div>

                            {error && (
                                <div className="p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-300 text-sm">
                                    {error}
                                </div>
                            )}

                            <div className="flex space-x-3 pt-4">
                                <button
                                    type="button"
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditingCategory(null);
                                        setError(null);
                                    }}
                                    className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={editLoading || !formData.name.trim()}
                                    className="flex-1 px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    {editLoading ? 'Updating...' : 'Update Category'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
}