#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('📄 Creating index.html for Cloudflare Pages...');

// 创建输出目录
const outputDir = '.vercel/output/static';
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 检查是否存在 Next.js 构建的 HTML 文件
const serverDir = '.next/server/app';
let indexContent = '';

if (fs.existsSync(path.join(serverDir, 'index.html'))) {
  // 如果存在预渲染的 index.html，使用它
  indexContent = fs.readFileSync(path.join(serverDir, 'index.html'), 'utf8');
  console.log('✅ Using pre-rendered index.html');
} else {
  // 创建基本的 index.html
  indexContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Free Games Online - Play Best Free Games</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Play the best free games online. No downloads required, play instantly in your browser!">
    <link rel="icon" href="/favicon.ico">
</head>
<body>
    <div id="__next">
        <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;">
            <div style="text-align: center;">
                <h1 style="color: #8b5cf6;">Free Games Online</h1>
                <p>Loading...</p>
            </div>
        </div>
    </div>
    <script>
        // 简单的客户端路由重定向
        if (window.location.pathname === '/') {
            // 这里可以添加一些基本的内容或重定向逻辑
            document.getElementById('__next').innerHTML = \`
                <div style="min-height: 100vh; background: linear-gradient(to bottom, rgba(139, 92, 246, 0.4), black); color: white; padding: 2rem;">
                    <div style="max-width: 1200px; margin: 0 auto;">
                        <h1 style="text-align: center; font-size: 3rem; margin-bottom: 2rem; background: linear-gradient(to right, #f472b6, #8b5cf6, #f472b6); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                            Free Games Online
                        </h1>
                        <p style="text-align: center; font-size: 1.2rem; margin-bottom: 3rem;">
                            Play the best free games online. No downloads required!
                        </p>
                        <div style="text-align: center;">
                            <p>Welcome to Free Games Online! This site is currently loading...</p>
                            <p>If you're seeing this message, the site is being set up. Please check back soon!</p>
                        </div>
                    </div>
                </div>
            \`;
        }
    </script>
</body>
</html>`;
  console.log('✅ Created basic index.html');
}

// 写入 index.html
fs.writeFileSync(path.join(outputDir, 'index.html'), indexContent);

// 复制静态资源
if (fs.existsSync('.next/static')) {
  const staticDir = path.join(outputDir, '_next/static');
  if (!fs.existsSync(staticDir)) {
    fs.mkdirSync(staticDir, { recursive: true });
  }
  
  // 在 Windows 上使用 xcopy，在其他系统上使用 cp
  const { execSync } = require('child_process');
  const isWindows = process.platform === 'win32';
  
  try {
    if (isWindows) {
      execSync(`xcopy ".next\\static" "${staticDir}" /E /I /Y`, { stdio: 'inherit' });
    } else {
      execSync(`cp -r .next/static/* ${staticDir}/`, { stdio: 'inherit' });
    }
    console.log('✅ Copied static assets');
  } catch (error) {
    console.log('⚠️ Warning: Could not copy static assets');
  }
}

// 创建 _headers 文件
const headersContent = `/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  X-XSS-Protection: 1; mode=block`;

fs.writeFileSync(path.join(outputDir, '_headers'), headersContent);

console.log('✅ Created _headers file');
console.log(`📂 Output ready in: ${outputDir}`);