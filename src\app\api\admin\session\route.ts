import { NextResponse } from 'next/server';

export const runtime = 'edge';

export async function GET(request: Request) {
  try {
    // 从请求中获取 cookie
    const cookieHeader = request.headers.get('cookie') || '';
    const cookies = parseCookies(cookieHeader);
    const sessionCookie = cookies['admin_session'];
    
    if (!sessionCookie) {
      return NextResponse.json({ isLoggedIn: false });
    }
    
    // 解析会话数据
    const session = JSON.parse(sessionCookie);
    
    // 检查会话是否有效
    const isValid = session && 
                   session.is_admin === true && 
                   new Date(session.expires) > new Date();
    
    return NextResponse.json({ isLoggedIn: isValid });
  } catch (error) {
    console.error('Session check error:', error);
    return NextResponse.json({ isLoggedIn: false });
  }
}

// 解析 cookie 字符串
function parseCookies(cookieHeader: string) {
  const cookies: Record<string, string> = {};
  cookieHeader.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=');
    if (name && value) {
      cookies[name] = decodeURIComponent(value);
    }
  });
  return cookies;
} 