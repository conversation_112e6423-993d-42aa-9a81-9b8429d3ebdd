'use client';
import React, { useState } from 'react';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    alert('Thank you for your message! We will get back to you soon.');
    setFormData({ name: '', email: '', message: '' });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <main className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Title Section */}
        <h1 className="text-5xl font-bold text-center mb-4 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
          Contact Us
        </h1>
        <p className="text-gray-300 text-center mb-12 max-w-4xl mx-auto">
          Questions about our free games? Suggestions for new games? Technical issues? We&apos;re here to help you enjoy the best free online gaming experience!
        </p>

        <div className="text-gray-300 text-center mb-12">
          <p>Our team is passionate about bringing you the best free online games. We value your feedback and are always looking for ways to improve your gaming experience.</p>
        </div>

        {/* Contact Methods Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text text-center">
            How to Contact Us
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">General Inquiries</h3>
              <p className="text-gray-300 mb-4">For any questions about our platform:</p>
              <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                <EMAIL>
              </a>
            </div>
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Technical Support</h3>
              <p className="text-gray-300 mb-4">Having issues with our games?</p>
              <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                <EMAIL>
              </a>
            </div>
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Game Submissions</h3>
              <p className="text-gray-300 mb-4">For developers wanting to submit games:</p>
              <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                <EMAIL>
              </a>
            </div>
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Advertising</h3>
              <p className="text-gray-300 mb-4">For advertising opportunities:</p>
              <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                <EMAIL>
              </a>
            </div>
            
            <div className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Response Time</h3>
              <p className="text-gray-300">
                We aim to respond to all inquiries within 48 hours. For urgent technical issues, please use our dedicated support email for faster assistance.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Form Section */}
        <div className="max-w-2xl mx-auto mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text text-center">
            Send Us a Message
          </h2>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 bg-gray-900 border border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 bg-gray-900 border border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white"
              />
            </div>

            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-1">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows={6}
                className="w-full px-4 py-2 bg-gray-900 border border-purple-600/30 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-white"
              ></textarea>
            </div>

            <button
              type="submit"
              className="w-full bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-white py-3 px-6 rounded-md hover:opacity-90 transition-opacity"
            >
              Send Message
            </button>
          </form>
        </div>

        {/* Final Note Section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Game On!
          </h2>
          <p className="text-gray-300 mb-4">
            We&apos;re constantly adding new games and features to enhance your free online gaming experience. Your feedback helps us improve, so don&apos;t hesitate to reach out with suggestions, report bugs, or just say hello!
          </p>
          <p className="text-gray-300">
            Thank you for being part of our gaming community. We look forward to hearing from you!
          </p>
        </div>
      </div>
    </main>
  );
}