'use client';

import React, { useState, useEffect } from 'react';

interface ClientMarkdownSectionProps {
  content?: string;
}

export default function ClientMarkdownSection({ content }: ClientMarkdownSectionProps) {
  // 在客户端动态导入EdgeMarkdownRenderer
  const [MarkdownComponent, setMarkdownComponent] = useState<React.ComponentType<{content: string}> | null>(null);
  
  useEffect(() => {
    // 在客户端动态导入组件
    import('../../components/EdgeMarkdownRenderer').then((mod) => {
      setMarkdownComponent(() => mod.default);
    });
  }, []);

  if (!content) return null;
  if (!MarkdownComponent) return <div>Loading content...</div>;
  
  return (
    <div className="mb-12">
      <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl border border-purple-600/30 p-8">
        <MarkdownComponent content={content} />
      </div>
    </div>
  );
} 