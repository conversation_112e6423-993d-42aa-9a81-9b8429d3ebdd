import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { checkSupabaseAvailable } from '@/lib/supabase-server';

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    // 检查 Supabase 配置是否可用
    const supabase = checkSupabaseAvailable();
    
    const { username, password } = await request.json();
    
    console.log("Attempting login for username:", username);
    
    // 直接查询数据库表
    const { data, error } = await supabase
      .from('free_games_online_categories_admin_users')
      .select()
      .eq('username', username)
      .single();
    
    if (error) {
      console.error("Database error:", error);
      return NextResponse.json(
        { message: 'Database error: ' + error.message },
        { status: 500 }
      );
    }
    
    if (!data) {
      console.log("User not found");
      return NextResponse.json(
        { message: 'Invalid credentials' },
        { status: 401 }
      );
    }
    
    console.log("User found, verifying password");
    
    // 验证密码
    const passwordValid = bcrypt.compareSync(password, data.password_hash);
    
    if (!passwordValid) {
      console.log("Password invalid");
      return NextResponse.json(
        { message: 'Invalid credentials' },
        { status: 401 }
      );
    }
    
    console.log("Password valid, creating session");
    
    // 更新最后登录时间
    await supabase
      .from('free_games_online_categories_admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', data.id);
    
    // 设置会话 cookie
    const session = {
      user_id: data.id,
      username: data.username,
      is_admin: true,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24小时后过期
    };
    
    // 创建响应并设置 cookie
    const response = NextResponse.json({ success: true });
    response.cookies.set({
      name: 'admin_session',
      value: JSON.stringify(session),
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 24, // 24小时
      path: '/'
    });
    
    console.log("Login successful");
    return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { message: 'Internal server error: ' + (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    );
  }
} 