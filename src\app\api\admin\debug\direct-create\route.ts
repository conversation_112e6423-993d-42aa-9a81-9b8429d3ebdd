import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { checkSupabaseAvailable } from '@/lib/supabase-server';

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    // 检查 Supabase 配置是否可用
    const supabase = checkSupabaseAvailable();
    
    const { username, password } = await request.json();
    
    if (!username || !password) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields'
      }, { status: 400 });
    }
    
    // 检查表是否存在
    const { error: tableError } = await supabase
      .from('free_games_online_categories_admin_users')
      .select('count()')
      .limit(1);
    
    // 如果表不存在，创建表
    if (tableError && tableError.message.includes('does not exist')) {
      // 使用 SQL 创建表
      const { error: createTableError } = await supabase.rpc('create_admin_table');
      
      if (createTableError) {
        // 如果 RPC 不存在，返回创建表的 SQL
        return NextResponse.json({
          success: false,
          error: 'Table does not exist',
          message: 'Please create the table first using the SQL provided',
          sql: `
CREATE TABLE free_games_online_categories_admin_users (
  id SERIAL PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  password_hash TEXT NOT NULL,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`
        }, { status: 500 });
      }
    }
    
    // 检查用户名是否已存在
    const { data: existingUser, error: checkError } = await supabase
      .from('free_games_online_categories_admin_users')
      .select('id')
      .eq('username', username)
      .single();
    
    if (!checkError && existingUser) {
      // 如果用户存在，更新密码
      const salt = bcrypt.genSaltSync(10);
      const hash = bcrypt.hashSync(password, salt);
      
      const { error: updateError } = await supabase
        .from('free_games_online_categories_admin_users')
        .update({ password_hash: hash })
        .eq('id', existingUser.id);
      
      if (updateError) {
        return NextResponse.json({
          success: false,
          error: 'Failed to update user',
          message: updateError.message
        }, { status: 500 });
      }
      
      return NextResponse.json({
        success: true,
        message: 'Admin user password updated successfully',
        userId: existingUser.id
      });
    }
    
    // 创建新用户
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(password, salt);
    
    const { data, error } = await supabase
      .from('free_games_online_categories_admin_users')
      .insert([
        { 
          username,
          password_hash: hash
        }
      ])
      .select();
    
    if (error) {
      return NextResponse.json({
        success: false,
        error: 'Failed to create user',
        message: error.message
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Admin user created successfully',
      user: {
        id: data[0].id,
        username: data[0].username,
        created_at: data[0].created_at
      },
      passwordHash: hash // 返回哈希值以便调试
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Unexpected error',
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 