const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
require('dotenv').config({ path: '.env.local' });

// Supabase client setup
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function updatePassword() {
  const username = process.argv[2] || 'braingatts';
  const password = process.argv[3] || 'admin123';
  
  console.log(`Updating password for user: ${username}`);
  console.log(`New password: ${password}`);
  
  // Generate hash
  const salt = bcrypt.genSaltSync(10);
  const hash = bcrypt.hashSync(password, salt);
  
  console.log(`Generated hash: ${hash}`);
  
  try {
    // Update password in database
    const { data, error } = await supabase
      .from('free_games_online_categories_admin_users')
      .update({ password_hash: hash })
      .eq('username', username)
      .select();
    
    if (error) {
      console.error('Error updating password:', error.message);
      return;
    }
    
    if (!data || data.length === 0) {
      console.error('User not found or no rows updated');
      return;
    }
    
    console.log('✓ Password updated successfully!');
    console.log('Updated user:', data[0]);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updatePassword().catch(console.error);