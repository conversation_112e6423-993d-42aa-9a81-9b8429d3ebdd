const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Supabase client setup
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabase() {
  console.log('Checking database...');

  // Check if admin table exists
  try {
    const { data: tables, error: tablesError } = await supabase
      .from('free_games_online_categories_admin_users')
      .select('id')
      .limit(1);

    if (tablesError) {
      console.error('Error checking admin table:', tablesError.message);
      if (tablesError.message.includes('does not exist')) {
        console.error('The table "free_games_online_categories_admin_users" does not exist!');
        console.log('\nCreate it with this SQL:');
        console.log(`
CREATE TABLE free_games_online_categories_admin_users (
  id SERIAL PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  password_hash TEXT NOT NULL,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
        `);
      }
    } else {
      console.log('✓ Admin table exists');
      
      // Check if admin user exists
      const { data: users, error: usersError } = await supabase
        .from('free_games_online_categories_admin_users')
        .select('*');
      
      if (usersError) {
        console.error('Error checking admin users:', usersError.message);
      } else if (!users || users.length === 0) {
        console.log('✗ No admin users found!');
        console.log('\nCreate an admin user with this SQL (replace the hash with your generated one):');
        console.log(`
INSERT INTO free_games_online_categories_admin_users (username, password_hash) 
VALUES ('admin', '$2b$10$IHAEMkwU6o4e2sOyh18guOxyO/qMGgdfGeQdAZVFWwKfHgZMlGVQO');
        `);
      } else {
        console.log('✓ Admin users found:', users.length);
        users.forEach((user, index) => {
          console.log(`\nUser ${index + 1}:`);
          console.log('  ID:', user.id);
          console.log('  Username:', user.username);
          console.log('  Password Hash:', user.password_hash ? '(set)' : '(not set)');
          console.log('  Last Login:', user.last_login || 'Never');
        });
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkDatabase().catch(console.error); 