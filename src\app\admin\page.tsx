import Link from 'next/link';

export default function AdminDashboard() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-pink-400 via-purple-400 to-pink-500 text-transparent bg-clip-text">
          Admin Dashboard
        </h1>
        <p className="text-gray-400">Welcome to the game management system</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Games Management */}
        <Link href="/admin/games" className="group">
          <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30 hover:border-purple-500/50 transition-all group-hover:scale-105">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold ml-4">Games</h3>
            </div>
            <p className="text-gray-400">Add, edit and manage game content</p>
          </div>
        </Link>

        {/* Categories Management */}
        <Link href="/admin/categories" className="group">
          <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30 hover:border-purple-500/50 transition-all group-hover:scale-105">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-600 to-red-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold ml-4">Categories</h3>
            </div>
            <p className="text-gray-400">Manage game categories</p>
          </div>
        </Link>

        {/* Tags Management */}
        <Link href="/admin/tags" className="group">
          <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30 hover:border-purple-500/50 transition-all group-hover:scale-105">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold ml-4">Tags</h3>
            </div>
            <p className="text-gray-400">Manage game tags</p>
          </div>
        </Link>

        {/* Debug Tools */}
        <Link href="/admin/debug" className="group">
          <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30 hover:border-purple-500/50 transition-all group-hover:scale-105">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold ml-4">Debug</h3>
            </div>
            <p className="text-gray-400">System debugging and development tools</p>
          </div>
        </Link>
      </div>

      {/* Quick Stats */}
      <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30">
        <h2 className="text-2xl font-semibold mb-4">Quick Stats</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">--</div>
            <div className="text-sm text-gray-400">Total Games</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-pink-400">--</div>
            <div className="text-sm text-gray-400">Total Tags</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-400">--</div>
            <div className="text-sm text-gray-400">Today's Views</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-400">--</div>
            <div className="text-sm text-gray-400">Active Users</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-900/60 backdrop-blur-sm p-6 rounded-xl border border-purple-600/30">
        <h2 className="text-2xl font-semibold mb-4">Quick Actions</h2>
        <div className="flex flex-wrap gap-4">
          <Link 
            href="/admin/games/new" 
            className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg transition-colors"
          >
            Add New Game
          </Link>
          <Link 
            href="/admin/categories/new" 
            className="px-4 py-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white rounded-lg transition-colors"
          >
            Add New Category
          </Link>
          <Link 
            href="/admin/tags/new" 
            className="px-4 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white rounded-lg transition-colors"
          >
            Add New Tag
          </Link>
          <Link 
            href="/" 
            target="_blank"
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            View Website
          </Link>
        </div>
      </div>
    </div>
  );
}