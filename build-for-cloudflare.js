#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building for Cloudflare Pages...');

// 检测操作系统
const isWindows = process.platform === 'win32';
const isCI = process.env.CI || process.env.CLOUDFLARE_PAGES;

function removeDir(dirPath) {
  if (fs.existsSync(dirPath)) {
    if (isWindows) {
      try {
        execSync(`rmdir /s /q "${dirPath}"`, { stdio: 'inherit' });
      } catch (e) {
        console.log(`Warning: Could not remove ${dirPath}`);
      }
    } else {
      execSync(`rm -rf "${dirPath}"`, { stdio: 'inherit' });
    }
  }
}

try {
  // 1. 清理之前的构建
  console.log('📦 Cleaning previous builds...');
  removeDir('.next');
  removeDir('.vercel');

  // 2. 运行 Next.js 构建
  console.log('🔨 Running Next.js build...');
  execSync('next build', { stdio: 'inherit' });

  // 3. 根据环境选择构建方法
  if (isCI && !isWindows) {
    // 在 CI/Linux 环境中使用 @cloudflare/next-on-pages
    console.log('☁️ Running Cloudflare Pages build (CI/Linux)...');
    try {
      execSync('npx @cloudflare/next-on-pages', { stdio: 'inherit' });
      console.log('✅ Cloudflare Pages build completed successfully!');
    } catch (error) {
      console.log('⚠️ Cloudflare Pages build failed, falling back to manual build...');
      throw error;
    }
  } else {
    // 在本地/Windows 环境中创建基本结构
    console.log('🔧 Creating basic build structure for local/Windows environment...');
    
    const outputDir = '.vercel/output/static';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 复制静态文件
    if (fs.existsSync('.next/static')) {
      const staticTarget = path.join(outputDir, '_next', 'static');
      if (!fs.existsSync(path.dirname(staticTarget))) {
        fs.mkdirSync(path.dirname(staticTarget), { recursive: true });
      }
      
      if (isWindows) {
        execSync(`xcopy ".next\\static" "${staticTarget}" /E /I /Y`, { stdio: 'inherit' });
      } else {
        execSync(`cp -r .next/static ${path.join(outputDir, '_next')}/`, { stdio: 'inherit' });
      }
      console.log('✅ Copied static assets');
    }

    // 创建基本的 index.html（用于本地测试）
    const indexHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Free Games Online - Play Best Free Games</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Play the best free games online. No downloads required, play instantly in your browser!">
    <link rel="icon" href="/favicon.ico">
    <style>
        body { 
            margin: 0; 
            font-family: Arial, sans-serif; 
            background: linear-gradient(to bottom, rgba(139, 92, 246, 0.4), black); 
            color: white; 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
        }
        .container { 
            text-align: center; 
            max-width: 800px; 
            padding: 2rem; 
        }
        h1 { 
            font-size: 3rem; 
            margin-bottom: 1rem; 
            background: linear-gradient(to right, #f472b6, #8b5cf6, #f472b6); 
            -webkit-background-clip: text; 
            -webkit-text-fill-color: transparent; 
            background-clip: text; 
        }
        p { 
            font-size: 1.2rem; 
            margin-bottom: 2rem; 
            opacity: 0.9; 
        }
        .note { 
            background: rgba(139, 92, 246, 0.2); 
            border: 1px solid rgba(139, 92, 246, 0.5); 
            border-radius: 8px; 
            padding: 1rem; 
            margin-top: 2rem; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Free Games Online</h1>
        <p>Play the best free games online. No downloads required!</p>
        <div class="note">
            <p><strong>Note:</strong> This is a local build preview. The full site with all features will be available when deployed to Cloudflare Pages.</p>
            <p>Features like dynamic routing, API endpoints, and database connectivity will work in the production environment.</p>
        </div>
    </div>
</body>
</html>`;
    
    fs.writeFileSync(path.join(outputDir, 'index.html'), indexHtml);

    // 创建 _headers 文件
    const headersContent = `/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  X-XSS-Protection: 1; mode=block`;
    
    fs.writeFileSync(path.join(outputDir, '_headers'), headersContent);

    console.log('✅ Basic build structure created for local testing');
  }

  console.log('✅ Build completed successfully!');
  console.log(`📂 Output directory: .vercel/output/static`);
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}