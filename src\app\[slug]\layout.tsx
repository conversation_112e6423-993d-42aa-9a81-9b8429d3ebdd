import React from 'react';
import GoogleAnalytics from '../components/GoogleAnalytics';
import GoogleAdsense from '../components/GoogleAdsense';
import HeadScript from '../components/HeadScript';

export default function GameLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <HeadScript />
      <GoogleAnalytics />
      <GoogleAdsense />
      {children}
    </>
  );
} 