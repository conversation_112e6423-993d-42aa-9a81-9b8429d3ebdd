const { createClient } = require('@supabase/supabase-js');

// 从 .env.local 读取配置
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Testing Supabase connection...');
console.log('URL:', supabaseUrl);
console.log('Key:', supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'NOT SET');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Environment variables not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    // 测试游戏表
    console.log('\n🎮 Testing games table...');
    const { data: games, error: gamesError } = await supabase
      .from('free_games_online_games')
      .select('id, title, slug, status')
      .eq('status', 'published')
      .limit(3);

    if (gamesError) {
      console.error('❌ Games query failed:', gamesError.message);
    } else {
      console.log('✅ Games found:', games?.length || 0);
      games?.forEach(game => console.log(`  - ${game.title} (${game.slug})`));
    }

    // 测试分类表
    console.log('\n📂 Testing categories table...');
    const { data: categories, error: categoriesError } = await supabase
      .from('free_games_online_categories')
      .select('id, name, slug')
      .limit(3);

    if (categoriesError) {
      console.error('❌ Categories query failed:', categoriesError.message);
    } else {
      console.log('✅ Categories found:', categories?.length || 0);
      categories?.forEach(cat => console.log(`  - ${cat.name} (${cat.slug})`));
    }

    // 测试 Sprunki 游戏
    console.log('\n🎵 Testing Sprunki games...');
    const { data: sprunkiGames, error: sprunkiError } = await supabase
      .from('free_games_online_games')
      .select(`
        *,
        free_games_online_categories!inner(name, slug)
      `)
      .eq('free_games_online_categories.slug', 'sprunki')
      .eq('status', 'published')
      .limit(3);

    if (sprunkiError) {
      console.error('❌ Sprunki games query failed:', sprunkiError.message);
    } else {
      console.log('✅ Sprunki games found:', sprunkiGames?.length || 0);
      sprunkiGames?.forEach(game => console.log(`  - ${game.title}`));
    }

  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
  }
}

testConnection();