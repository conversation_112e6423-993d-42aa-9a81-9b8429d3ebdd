import { createClient } from '@supabase/supabase-js';

// 环境变量中获取Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// 确保环境变量已设置
if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  console.warn('Missing Supabase environment variables. Please check your .env.local file.');
}

// 创建Supabase客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 类型定义
export type Game = {
  id: string;
  title: string;
  slug: string;
  short_description: string;
  markdown_content?: string;
  cover_image: string;
  screenshots?: string[];
  embed_code?: string;
  rating: number;
  play_count: number;
  is_featured: boolean;
  status: 'draft' | 'published' | 'archived';
  category_id?: string;
  created_at: string;
  updated_at: string;
  // 关联数据
  free_games_online_categories?: {
    id: string;
    name: string;
    slug: string;
  };
  free_games_online_game_tags?: Array<{
    free_games_online_tags: {
      id: string;
      name: string;
      slug: string;
    }
  }>;
};

export type Category = {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image_url?: string;
  game_count: number;
  display_order: number;
  created_at: string;
  updated_at: string;
};

export type Tag = {
  id: string;
  name: string;
  slug: string;
  created_at: string;
}; 