import React from 'react';
import Link from 'next/link';
import { supabase } from '@/lib/supabase';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'All Tags | free-gamesonline.com',
  description: 'Browse all game tags on Free Games Online. Find games by category and play instantly in your browser!',
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://free-gamesonline.com'}/tags`,
  },
  openGraph: {
    title: 'All Tags | free-gamesonline.com',
    description: 'Browse all game tags on Free Games Online. Find games by category and play instantly in your browser!',
    url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://free-gamesonline.com'}/tags`,
  },
  twitter: {
    card: 'summary',
    title: 'All Tags | free-gamesonline.com',
    description: 'Browse all game tags on Free Games Online. Find games by category and play instantly in your browser!',
  }
};

// 获取所有标签
async function getAllTags() {
  const { data: tags } = await supabase
    .from('free_games_online_tags')
    .select('id, name, slug')
    .order('name');
  
  return tags || [];
}

export default async function TagsPage() {
  const tags = await getAllTags();
  
  return (
    <main className="min-h-screen bg-gradient-to-b from-purple-900/40 via-black to-black text-white">
      <div className="container mx-auto px-4 py-12">
        {/* 面包屑导航 */}
        <nav className="flex items-center text-base mb-8">
          <Link href="/" className="text-white hover:text-purple-300 font-medium transition-colors">Home</Link>
          <span className="mx-2 text-white">/</span>
          <span className="text-white font-medium">Tags</span>
        </nav>
        
        {/* 标题和描述 */}
        <div className="mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-pink-400 via-purple-400 to-pink-500 text-transparent bg-clip-text drop-shadow-lg">
            All Game Tags
          </h1>
          <p className="text-lg text-gray-300 max-w-3xl">
            Browse all game tags on Free Games Online. Click on a tag to see all games in that category.
          </p>
        </div>
        
        {/* 标签列表 */}
        {tags.length === 0 ? (
          <div className="text-center py-12 bg-black/20 rounded-xl">
            <p className="text-gray-400 text-lg">No tags found.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {tags.map(tag => (
              <Link 
                href={`/tag/${tag.slug}`} 
                key={tag.id}
                className="bg-gradient-to-br from-purple-900/20 to-pink-900/10 p-[1px] rounded-lg hover:from-purple-600/20 hover:to-pink-600/20 transition-all duration-300"
              >
                <div className="bg-gray-900/60 backdrop-blur-sm rounded-lg p-4 text-center h-full flex items-center justify-center">
                  <span className="text-white font-medium hover:text-purple-300 transition-colors">
                    {tag.name}
                  </span>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </main>
  );
} 