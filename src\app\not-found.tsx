import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '404 - Page Not Found | Free Games Online',
  description: 'Sorry, the page you are looking for does not exist. Return to our homepage to discover awesome free online games.',
  keywords: 'page not found, 404, error page, free games, online games',
  robots: 'noindex, follow',
  alternates: {
    canonical: 'https://free-gamesonline.com/404',
  },
  openGraph: {
    title: '404 - Page Not Found | Free Games Online',
    description: 'Sorry, the page you are looking for does not exist. Return to our homepage to discover awesome free online games.',
    type: 'website',
  }
};

export default function NotFound() {
  return (
    <div className="min-h-[80vh] flex items-center justify-center bg-black text-white">
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
          404 - Page Not Found
        </h1>
        <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          Oops! The page you&apos;re looking for doesn&apos;t seem to exist. Don&apos;t worry, you can always return to continue your gaming adventure.
        </p>
        <div className="space-y-4">
          <Link
            href="/"
            className="inline-block bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-white py-3 px-8 rounded-md hover:opacity-90 transition-all font-medium text-lg"
          >
            Return to Home
          </Link>
          <p className="text-gray-400 mt-6">
            Want to explore more games? Check out our{' '}
            <Link href="/sprunki" className="text-purple-400 hover:text-purple-300 transition-colors underline">
              Sprunki Game Series
            </Link>
            {' '}or visit{' '}
            <Link href="/sprunki-sky-treatment" className="text-purple-400 hover:text-purple-300 transition-colors underline">
              Popular Games
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
