'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import { PlayIcon } from '@heroicons/react/24/solid';

interface GamePlayerProps {
  embedUrl: string;
  coverImage: string;
  title: string;
}

const GamePlayer: React.FC<GamePlayerProps> = ({ embedUrl, coverImage, title }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const gameContainerRef = useRef<HTMLDivElement>(null);

  const startGame = () => {
    setIsPlaying(true);
  };

  const toggleFullscreen = async () => {
    if (!gameContainerRef.current) return;

    try {
      if (!document.fullscreenElement) {
        await gameContainerRef.current.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
    }
  };

  return (
    <div className="w-full bg-black rounded-lg overflow-hidden border border-purple-600/30 mb-8">
      {isPlaying ? (
        <div ref={gameContainerRef} className="aspect-[3/4] lg:aspect-video w-full relative group">
          <iframe
            src={embedUrl}
            title={`Play ${title} online`}
            className="w-full h-full"
            allowFullScreen
            allow="fullscreen; autoplay; encrypted-media"
          />
          
          {/* Fullscreen Button */}
          <button
            onClick={toggleFullscreen}
            className="absolute bottom-2 right-2 sm:bottom-4 sm:right-4 bg-black/70 hover:bg-black/90 text-white p-1.5 sm:p-2 rounded-lg opacity-100 sm:opacity-0 sm:group-hover:opacity-100 transition-opacity duration-300 z-10"
            title="Toggle Fullscreen"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-5 w-5" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" 
              />
            </svg>
          </button>
        </div>
      ) : (
        <div className="aspect-[3/4] lg:aspect-video w-full relative flex flex-col items-center justify-center">
          {/* Blurred background cover image */}
          <div className="absolute inset-0 z-0">
            <Image
              src={coverImage}
              alt=""
              fill
              className="object-cover blur-sm brightness-50"
              priority
            />
          </div>
          
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-purple-900/40 to-black/80 z-10"></div>
          
          {/* Content */}
          <div className="z-20 flex flex-col items-center px-4">
            <div className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-64 lg:h-64 relative rounded-full overflow-hidden border-2 sm:border-3 md:border-4 border-purple-500/50 shadow-lg shadow-purple-500/30 mb-4 sm:mb-6">
              <Image
                src={coverImage}
                alt={title}
                fill
                className="object-cover"
                priority
              />
            </div>
            
            <button
              onClick={startGame}
              className="flex items-center justify-center gap-1 sm:gap-2 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-bold py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base rounded-full transform transition-all hover:scale-105 shadow-lg"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6" viewBox="0 0 24 24" fill="currentColor">
                <path fillRule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.28 19.991c-1.25.687-2.779-.217-2.779-1.643V5.653z" clipRule="evenodd" />
              </svg>
              <span>Play Now</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GamePlayer; 