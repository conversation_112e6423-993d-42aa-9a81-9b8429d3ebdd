import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { Metadata, ResolvingMetadata } from 'next';
import { supabase } from '@/lib/supabase';

export const runtime = 'edge';

// 动态生成元数据
export async function generateMetadata(
  { params }: { params: Promise<{ slug: string }> },
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { slug } = await params;
  
  // 获取标签数据
  const { data: tag } = await supabase
    .from('free_games_online_tags')
    .select('name')
    .eq('slug', slug)
    .single();
  
  if (!tag) {
    return {
      title: 'Tag Not Found',
      description: 'The requested tag could not be found.'
    };
  }
  
  // 构建正确的 canonical URL
  const canonicalUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://free-gamesonline.com'}/tag/${slug}`;
  
  return {
    title: `${tag.name} Games | free-gamesonline.com`,
    description: `Play the best free ${tag.name} games online. No downloads required, play instantly in your browser!`,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: `${tag.name} Games | free-gamesonline.com`,
      description: `Play the best free ${tag.name} games online. No downloads required, play instantly in your browser!`,
      url: canonicalUrl,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${tag.name} Games | free-gamesonline.com`,
      description: `Play the best free ${tag.name} games online. No downloads required, play instantly in your browser!`,
    }
  };
}

// 游戏类型定义
interface Game {
  id: string;
  title: string;
  slug: string;
  short_description: string;
  cover_image: string;
  rating: number;
  status?: string;
}

// 获取标签下的游戏
async function getGamesByTag(tagSlug: string, page = 1, limit = 12) {
  // 计算分页偏移量
  const offset = (page - 1) * limit;
  
  // 获取标签信息
  const { data: tag } = await supabase
    .from('free_games_online_tags')
    .select('id, name')
    .eq('slug', tagSlug)
    .single();
  
  if (!tag) return { games: [], total: 0, pages: 0, tag: null };
  
  // 获取游戏总数
  const { count } = await supabase
    .from('free_games_online_game_tags')
    .select('game_id', { count: 'exact' })
    .eq('tag_id', tag.id);
  
  const total = count || 0;
  const pages = Math.ceil(total / limit);
  
  // 获取游戏
  const { data: gameRelations } = await supabase
    .from('free_games_online_game_tags')
    .select(`
      game_id,
      free_games_online_games:game_id (
        id, title, slug, short_description, cover_image, rating, status
      )
    `)
    .eq('tag_id', tag.id)
    .order('game_id', { ascending: false })
    .range(offset, offset + limit - 1);
  
  // 提取游戏数据
  const games = gameRelations
    ? gameRelations
        .map(relation => relation.free_games_online_games as unknown as Game)
        .filter(game => game && game.status === 'published')
    : [];
  
  return { games, total, pages, tag };
}

// 标签页面组件
export default async function TagPage({ 
  params,
  searchParams 
}: { 
  params: Promise<{ slug: string }>,
  searchParams: Promise<{ page?: string }>
}) {
  const { slug } = await params;
  const { page } = await searchParams;
  
  // 获取当前页码，默认为第1页
  const currentPage = page ? parseInt(page) : 1;
  const gamesPerPage = 12;
  
  // 获取标签下的游戏
  const { games, total, pages, tag } = await getGamesByTag(slug, currentPage, gamesPerPage);
  
  // 如果标签不存在，返回404
  if (!tag) {
    notFound();
  }
  
  // 渲染分页控件
  function renderPaginationNumbers(currentPage: number, totalPages: number) {
    const pages = [];
    
    // 计算应显示的页码范围
    if (totalPages <= 7) {
      // 少于7页时，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 始终显示第一页
      pages.push(1);
      
      // 计算显示范围
      let rangeStart = Math.max(2, currentPage - 2);
      let rangeEnd = Math.min(totalPages - 1, currentPage + 2);
      
      // 如果靠近开头，调整结束位置
      if (rangeStart <= 3) {
        rangeStart = 2;
        rangeEnd = Math.min(6, totalPages - 1);
      }
      
      // 如果靠近结尾，调整开始位置
      if (rangeEnd >= totalPages - 2) {
        rangeEnd = totalPages - 1;
        rangeStart = Math.max(2, totalPages - 5);
      }
      
      // 添加第一页和当前范围之间的省略号
      if (rangeStart > 2) {
        pages.push('...');
      }
      
      // 添加当前页周围的页码
      for (let i = rangeStart; i <= rangeEnd; i++) {
        pages.push(i);
      }
      
      // 添加当前范围和最后一页之间的省略号
      if (rangeEnd < totalPages - 1) {
        pages.push('...');
      }
      
      // 始终显示最后一页
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages.map((page, index) => {
      if (page === '...') {
        return (
          <span key={`ellipsis-${index}`} className="px-3 py-2 bg-gray-800/60 text-gray-400 rounded-lg">
            ...
          </span>
        );
      }
      
      return (
        <Link
          key={`page-${page}`}
          href={`/tag/${slug}?page=${page}`}
          className={`px-3 py-2 rounded-lg ${
            currentPage === page
              ? 'bg-purple-600 text-white'
              : 'bg-gray-800/60 text-gray-300 hover:bg-gray-700/60'
          }`}
        >
          {page}
        </Link>
      );
    });
  }
  
  return (
    <main className="min-h-screen bg-gradient-to-b from-purple-900/40 via-black to-black text-white">
      <div className="container mx-auto px-4 py-12">
        {/* 面包屑导航 */}
        <nav className="flex items-center text-base mb-8">
          <Link href="/" className="text-white hover:text-purple-300 font-medium transition-colors">Home</Link>
          <span className="mx-2 text-white">/</span>
          <Link href="/tags" className="text-white hover:text-purple-300 font-medium transition-colors">Tags</Link>
          <span className="mx-2 text-white">/</span>
          <span className="text-white font-medium">{tag.name}</span>
        </nav>
        
        {/* 标题和描述 */}
        <div className="mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-pink-400 via-purple-400 to-pink-500 text-transparent bg-clip-text drop-shadow-lg">
            {tag.name} Games
          </h1>
          <p className="text-lg text-gray-300 max-w-3xl">
            Discover and play the best free {tag.name} games online. No downloads required, play instantly in your browser!
          </p>
        </div>
        
        {/* 游戏卡片网格 */}
        {games.length === 0 ? (
          <div className="text-center py-12 bg-black/20 rounded-xl">
            <p className="text-gray-400 text-lg">No games found with this tag.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-12">
            {games.map(game => (
              <Link href={`/${game.slug}`} key={game.id} className="group">
                <div className="bg-gradient-to-br from-purple-900/20 to-pink-900/10 p-[1px] rounded-xl hover:from-purple-600/20 hover:to-pink-600/20 transition-all duration-300">
                  <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl overflow-hidden h-full">
                    <div className="relative h-48 overflow-hidden">
                      <Image
                        src={game.cover_image}
                        alt={game.title}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="text-lg font-bold group-hover:text-purple-400 transition-colors">{game.title}</h3>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
        
        {/* 分页 */}
        {pages > 1 && (
          <div className="flex justify-center gap-2">
            <Link 
              href={`/tag/${slug}?page=${Math.max(currentPage - 1, 1)}`}
              className={`px-4 py-2 rounded-lg ${
                currentPage === 1 
                  ? 'bg-gray-800/60 text-gray-500 cursor-not-allowed pointer-events-none' 
                  : 'bg-purple-600 hover:bg-purple-700 text-white'
              }`}
            >
              Previous
            </Link>
            
            <div className="flex gap-1">
              {renderPaginationNumbers(currentPage, pages)}
            </div>
            
            <Link 
              href={`/tag/${slug}?page=${Math.min(currentPage + 1, pages)}`}
              className={`px-4 py-2 rounded-lg ${
                currentPage === pages 
                  ? 'bg-gray-800/60 text-gray-500 cursor-not-allowed pointer-events-none' 
                  : 'bg-purple-600 hover:bg-purple-700 text-white'
              }`}
            >
              Next
            </Link>
          </div>
        )}
      </div>
    </main>
  );
} 