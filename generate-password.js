const bcrypt = require('bcryptjs');

// The password to hash
const password = process.argv[2] || 'admin';

// Generate salt and hash
const salt = bcrypt.genSaltSync(10);
const hash = bcrypt.hashSync(password, salt);

console.log('Password:', password);
console.log('Hashed Password:', hash);
console.log('\nSQL Command:');
console.log(`INSERT INTO free_games_online_categories_admin_users (username, password_hash) VALUES ('admin', '${hash}');`); 