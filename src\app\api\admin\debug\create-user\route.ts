import { NextResponse } from 'next/server';
import { checkSupabaseAvailable } from '@/lib/supabase-server';

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    // 检查 Supabase 配置是否可用
    const supabase = checkSupabaseAvailable();
    
    const { username, passwordHash } = await request.json();
    
    if (!username || !passwordHash) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields',
      }, { status: 400 });
    }
    
    // 检查用户名是否已存在
    const { data: existingUser, error: checkError } = await supabase
      .from('free_games_online_categories_admin_users')
      .select('id')
      .eq('username', username)
      .single();
    
    if (checkError && !checkError.message.includes('No rows found')) {
      return NextResponse.json({
        success: false,
        error: 'Database error',
        message: checkError.message
      }, { status: 500 });
    }
    
    if (existingUser) {
      return NextResponse.json({
        success: false,
        error: 'User already exists',
        message: `Username "${username}" is already taken`
      }, { status: 400 });
    }
    
    // 插入新用户
    const { data, error } = await supabase
      .from('free_games_online_categories_admin_users')
      .insert([
        { 
          username,
          password_hash: passwordHash
        }
      ])
      .select();
    
    if (error) {
      return NextResponse.json({
        success: false,
        error: 'Failed to create user',
        message: error.message
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Admin user created successfully',
      user: data[0]
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Unexpected error',
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 