'use client';
import { useState, useEffect } from 'react';
import bcrypt from 'bcryptjs';

export default function DebugPage() {
  const [dbStatus, setDbStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [username, setUsername] = useState('admin');
  const [password, setPassword] = useState('admin');
  const [passwordHash, setPasswordHash] = useState('');
  const [createUserStatus, setCreateUserStatus] = useState<any>(null);
  
  // Check database status
  useEffect(() => {
    async function checkDb() {
      try {
        const response = await fetch('/api/admin/debug');
        const data = await response.json();
        setDbStatus(data);
      } catch (err) {
        setError('Failed to check database status');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }
    
    checkDb();
  }, []);
  
  // Generate password hash
  const generateHash = () => {
    try {
      const salt = bcrypt.genSaltSync(10);
      const hash = bcrypt.hashSync(password, salt);
      setPasswordHash(hash);
    } catch (err) {
      setError('Failed to generate hash');
      console.error(err);
    }
  };
  
  // Create admin user
  const createAdminUser = async () => {
    if (!passwordHash) {
      setError('Please generate password hash first');
      return;
    }
    
    try {
      const response = await fetch('/api/admin/debug/create-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          passwordHash,
        }),
      });
      
      const data = await response.json();
      setCreateUserStatus(data);
    } catch (err) {
      setError('Failed to create user');
      console.error(err);
    }
  };
  
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Admin Debug Page</h1>
      
      {error && (
        <div className="bg-red-500/20 border border-red-500 text-red-300 p-4 mb-4 rounded">
          {error}
        </div>
      )}
      
      <div className="bg-gray-800 p-4 rounded mb-6">
        <h2 className="text-xl font-bold mb-2">Database Status</h2>
        {loading ? (
          <p>Loading...</p>
        ) : (
          <pre className="bg-black/50 p-4 rounded overflow-auto max-h-60">
            {JSON.stringify(dbStatus, null, 2)}
          </pre>
        )}
      </div>
      
      <div className="bg-gray-800 p-4 rounded mb-6">
        <h2 className="text-xl font-bold mb-2">Create Admin User</h2>
        
        <div className="mb-4">
          <label className="block mb-1">Username</label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            className="w-full bg-gray-900 border border-gray-700 rounded p-2"
          />
        </div>
        
        <div className="mb-4">
          <label className="block mb-1">Password</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full bg-gray-900 border border-gray-700 rounded p-2"
          />
        </div>
        
        <button
          onClick={generateHash}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded mr-2"
        >
          Generate Hash
        </button>
        
        {passwordHash && (
          <div className="mt-4">
            <p className="mb-2">Password Hash:</p>
            <pre className="bg-black/50 p-2 rounded overflow-auto">{passwordHash}</pre>
            
            <button
              onClick={createAdminUser}
              className="mt-4 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
            >
              Create Admin User
            </button>
          </div>
        )}
        
        {createUserStatus && (
          <div className="mt-4">
            <h3 className="font-bold mb-2">Result:</h3>
            <pre className="bg-black/50 p-2 rounded overflow-auto">
              {JSON.stringify(createUserStatus, null, 2)}
            </pre>
          </div>
        )}
      </div>
      
      <div className="bg-gray-800 p-4 rounded">
        <h2 className="text-xl font-bold mb-2">SQL Commands</h2>
        
        <div className="mb-4">
          <h3 className="font-bold mb-1">Create Table:</h3>
          <pre className="bg-black/50 p-2 rounded overflow-auto">
{`CREATE TABLE free_games_online_categories_admin_users (
  id SERIAL PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  password_hash TEXT NOT NULL,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`}
          </pre>
        </div>
        
        {passwordHash && (
          <div>
            <h3 className="font-bold mb-1">Insert Admin User:</h3>
            <pre className="bg-black/50 p-2 rounded overflow-auto">
{`INSERT INTO free_games_online_categories_admin_users (username, password_hash) 
VALUES ('${username}', '${passwordHash}');`}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
} 