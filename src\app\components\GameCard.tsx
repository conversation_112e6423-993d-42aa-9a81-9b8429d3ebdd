import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Game } from '@/lib/supabase';

interface GameCardProps {
  game: Game;
  size?: 'small' | 'medium' | 'large';
}

export default function GameCard({ game, size = 'medium' }: GameCardProps) {
  // 根据尺寸确定样式
  const sizeClasses = {
    small: {
      container: "w-full",
      imageContainer: "aspect-[4/3]",
      title: "text-sm font-medium",
      description: "text-xs line-clamp-2",
    },
    medium: {
      container: "w-full",
      imageContainer: "aspect-[4/3]",
      title: "text-base font-semibold",
      description: "text-sm line-clamp-2",
    },
    large: {
      container: "w-full",
      imageContainer: "aspect-[16/9]",
      title: "text-lg font-bold",
      description: "text-base line-clamp-3",
    }
  };

  const classes = sizeClasses[size];
  
  // 获取分类 slug，如果没有则使用默认值
  const categorySlug = game.free_games_online_categories?.slug || 'sprunki';

  return (
    <Link 
      href={`/${categorySlug}/${game.slug}`} 
      className={`${classes.container} block bg-gray-900 rounded-lg overflow-hidden border border-purple-600/30 hover:border-purple-600 transition-all transform hover:scale-105 hover:shadow-[0_0_15px_rgba(147,51,234,0.3)]`}
    >
      <div className={`${classes.imageContainer} relative bg-purple-900/20`}>
        <Image 
          src={game.cover_image} 
          alt={game.title}
          fill
          className="object-cover"
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
        />
        {game.rating > 0 && (
          <div className="absolute top-2 right-2 bg-black/70 text-yellow-400 text-xs font-bold px-2 py-1 rounded">
            ★ {game.rating.toFixed(1)}
          </div>
        )}
      </div>
      <div className="p-3">
        <h3 className={`${classes.title} text-white hover:text-purple-400 transition-colors`}>
          {game.title}
        </h3>
        {size !== 'small' && (
          <p className={`${classes.description} text-gray-400 mt-1`}>
            {game.short_description}
          </p>
        )}
      </div>
    </Link>
  );
} 