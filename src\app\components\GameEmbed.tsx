'use client';
import { useState } from 'react';

interface GameEmbedProps {
  embedUrl: string;
}

export default function GameEmbed({ embedUrl }: GameEmbedProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);

  const toggleFullscreen = () => {
    const gameContainer = document.getElementById('game-container');
    if (!document.fullscreenElement) {
      gameContainer?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <div className="relative w-full mx-auto aspect-[16/9] bg-gray-900 rounded-lg overflow-hidden mb-8 border-2 border-purple-600 shadow-[0_0_15px_rgba(147,51,234,0.5)] p-1" id="game-container">
      <iframe
        src={embedUrl}
        className="w-full h-full border-0 rounded-lg"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
      />
      <button
        onClick={toggleFullscreen}
        className="absolute bottom-4 right-4 bg-black/40 backdrop-blur-sm text-white/90 px-4 py-2 rounded-md hover:bg-black/60 transition-all font-medium"
      >
        {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
      </button>
    </div>
  );
} 