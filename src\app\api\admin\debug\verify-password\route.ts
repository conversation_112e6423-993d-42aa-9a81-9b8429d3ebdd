import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';

export const runtime = 'edge';

export async function POST(request: Request) {
  try {
    const { password, storedHash } = await request.json();
    
    if (!password || !storedHash) {
      return NextResponse.json({
        error: 'Missing required fields',
        isValid: false
      }, { status: 400 });
    }
    
    // 尝试不同的验证方法
    const results = {
      // 标准 bcrypt 比较
      standard: false,
      // 尝试修复可能的编码问题
      withTrim: false,
      // 尝试使用不同的 bcrypt 版本
      withDifferentSalt: false,
      // 原始哈希和密码
      password,
      storedHash
    };
    
    try {
      results.standard = bcrypt.compareSync(password, storedHash);
    } catch (e) {
      results.standard = false;
    }
    
    try {
      results.withTrim = bcrypt.compareSync(password.trim(), storedHash.trim());
    } catch (e) {
      results.withTrim = false;
    }
    
    // 尝试生成新的哈希并比较格式
    const newHash = bcrypt.hashSync(password, 10);
    results.withDifferentSalt = bcrypt.compareSync(password, newHash);
    
    const isValid = results.standard || results.withTrim;
    
    return NextResponse.json({
      isValid,
      details: results
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Unexpected error',
      message: error instanceof Error ? error.message : String(error),
      isValid: false
    }, { status: 500 });
  }
} 