/* Twikoo 深色主题自定义样式 */
.twikoo-dark-theme {
  color-scheme: dark;
}

/* 主要文字颜色 */
.twikoo-dark-theme .tk-content,
.twikoo-dark-theme .tk-nick,
.twikoo-dark-theme .tk-time,
.twikoo-dark-theme .tk-comment,
.twikoo-dark-theme .tk-input,
.twikoo-dark-theme .tk-textarea,
.twikoo-dark-theme .tk-submit,
.twikoo-dark-theme .tk-replies .tk-content,
.twikoo-dark-theme .tk-replies .tk-nick,
.twikoo-dark-theme .tk-replies .tk-time {
  color: #e5e7eb !important;
}

/* 输入框样式 - 加深边框 */
.twikoo-dark-theme .tk-input,
.twikoo-dark-theme .tk-textarea {
  background-color: #374151 !important;
  border: 2px solid #1f2937 !important;
  color: #f3f4f6 !important;
  border-radius: 6px !important;
}

.twikoo-dark-theme .tk-input:focus,
.twikoo-dark-theme .tk-textarea:focus {
  border-color: #374151 !important;
  box-shadow: 0 0 0 2px #4b5563 !important;
}

/* 按钮样式 - 去掉紫色 */
.twikoo-dark-theme .tk-submit {
  background-color: #374151 !important;
  color: white !important;
  border: 2px solid #4b5563 !important;
  border-radius: 6px !important;
}

.twikoo-dark-theme .tk-submit:hover {
  background-color: #4b5563 !important;
  border-color: #6b7280 !important;
}

/* 回复按钮和操作按钮 - 去掉紫色 */
.twikoo-dark-theme .tk-action-icon,
.twikoo-dark-theme .tk-replies-expand,
.twikoo-dark-theme .tk-reply {
  color: #9ca3af !important;
}

.twikoo-dark-theme .tk-action-icon:hover,
.twikoo-dark-theme .tk-replies-expand:hover,
.twikoo-dark-theme .tk-reply:hover {
  color: #d1d5db !important;
}

/* 评论卡片背景 - 去掉紫色边框 */
.twikoo-dark-theme .tk-comment {
  background-color: rgba(55, 65, 81, 0.3) !important;
  border: 1px solid #4b5563 !important;
  border-radius: 8px !important;
}

/* 回复区域 - 去掉紫色 */
.twikoo-dark-theme .tk-replies {
  border-left: 2px solid #4b5563 !important;
  background-color: rgba(31, 41, 55, 0.3) !important;
}

/* 头像边框 */
.twikoo-dark-theme .tk-avatar {
  border: 2px solid #6b7280 !important;
}

/* 链接颜色 - 去掉紫色 */
.twikoo-dark-theme a {
  color: #9ca3af !important;
}

.twikoo-dark-theme a:hover {
  color: #d1d5db !important;
}

/* 占位符文字 */
.twikoo-dark-theme .tk-input::placeholder,
.twikoo-dark-theme .tk-textarea::placeholder {
  color: #9ca3af !important;
}

/* 表情选择器 */
.twikoo-dark-theme .tk-owo-container {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
}

/* 加载状态 - 去掉紫色 */
.twikoo-dark-theme .tk-loading {
  color: #9ca3af !important;
}

/* 错误信息 */
.twikoo-dark-theme .tk-error {
  color: #f87171 !important;
}

/* 成功信息 */
.twikoo-dark-theme .tk-success {
  color: #34d399 !important;
}

/* 隐藏 Powered by Twikoo 文字 */
.twikoo-dark-theme .tk-copyright,
.twikoo-dark-theme .tk-powered,
.twikoo-dark-theme .tk-footer,
.twikoo-dark-theme [class*="powered"],
.twikoo-dark-theme [class*="copyright"] {
  display: none !important;
}

/* SEO 优化：防止搜索引擎过度关注用户生成内容 */
.twikoo-dark-theme {
  /* 防止文本选择，降低搜索引擎对内容的重视程度 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  
  /* 添加 aria 标签表明这是用户生成内容 */
  position: relative;
}

/* 为评论区添加语义化标记 */
.twikoo-dark-theme::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  pointer-events: none;
  /* 使用 CSS 属性标记这是用户生成内容区域 */
  --content-type: "user-generated";
}

/* 进一步降低评论内容在 SEO 中的权重 */
.twikoo-dark-theme .tk-comment,
.twikoo-dark-theme .tk-replies {
  /* 使用较低的 z-index 降低内容优先级 */
  position: relative;
  z-index: 1;
}

/* 确保评论内容不会被搜索引擎当作主要内容 */
.twikoo-dark-theme * {
  /* 防止内容被复制，进一步表明这不是主要内容 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}