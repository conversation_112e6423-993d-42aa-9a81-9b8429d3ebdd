'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import EdgeMarkdownRenderer component to avoid server-side rendering issues
const EdgeMarkdownRenderer = dynamic(
  () => import('../../components/EdgeMarkdownRenderer'),
  { 
    ssr: false,
    loading: () => <div className="p-4 text-gray-400">Loading content...</div>
  }
);

interface ClientSideMarkdownProps {
  content: string;
}

export default function ClientSideMarkdown({ content }: ClientSideMarkdownProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Confirm we're now in client environment
    setIsClient(true);
  }, []);

  if (!content) return null;
  
  // Only show content when rendering on client
  if (!isClient) {
    return <div className="mb-12 p-4 text-gray-400">Loading content...</div>;
  }

  return (
    <div className="mb-12">
      <div className="bg-gray-900/60 backdrop-blur-sm rounded-xl border border-purple-600/30 p-8">
        <EdgeMarkdownRenderer content={content} />
      </div>
    </div>
  );
} 