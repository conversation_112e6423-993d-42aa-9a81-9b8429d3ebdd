'use client';

export default function TestEnvPage() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Environment Variables Test</h1>
      
      <div className="bg-gray-900 p-6 rounded-lg">
        <h2 className="text-xl font-bold mb-4 text-purple-400">Supabase Configuration</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-1">
            NEXT_PUBLIC_SUPABASE_URL:
          </label>
          <div className="bg-gray-800 p-2 rounded text-gray-300">
            {supabaseUrl || 'NOT SET'}
          </div>
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-1">
            NEXT_PUBLIC_SUPABASE_ANON_KEY:
          </label>
          <div className="bg-gray-800 p-2 rounded text-gray-300">
            {supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'NOT SET'}
          </div>
        </div>
        
        <div className="mt-6">
          <h3 className="text-lg font-bold mb-2 text-purple-400">Status</h3>
          <div className={`p-3 rounded ${supabaseUrl && supabaseKey ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'}`}>
            {supabaseUrl && supabaseKey ? '✅ Environment variables are configured' : '❌ Environment variables are missing'}
          </div>
        </div>
      </div>
    </div>
  );
}