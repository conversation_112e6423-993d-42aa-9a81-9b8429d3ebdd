import { NextResponse } from 'next/server';
import { checkSupabaseAvailable } from '@/lib/supabase-server';

export const runtime = 'edge';

export async function GET() {
  try {
    // 检查 Supabase 配置是否可用
    const supabase = checkSupabaseAvailable();
    
    // 检查表是否存在
    const { data: tableInfo, error: tableError } = await supabase
      .from('free_games_online_categories_admin_users')
      .select('count()')
      .limit(1);
    
    if (tableError) {
      return NextResponse.json({
        error: 'Table error',
        message: tableError.message,
        hint: 'The table might not exist. Create it using the SQL in the documentation.'
      }, { status: 500 });
    }
    
    // 获取所有用户
    const { data: users, error: usersError } = await supabase
      .from('free_games_online_categories_admin_users')
      .select('id, username, created_at, last_login')
      .order('created_at', { ascending: false });
    
    if (usersError) {
      return NextResponse.json({
        error: 'Users query error',
        message: usersError.message
      }, { status: 500 });
    }
    
    return NextResponse.json({
      tableExists: true,
      userCount: users?.length || 0,
      users: users || []
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Unexpected error',
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 