import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import GoogleAnalytics from './components/GoogleAnalytics';
import GoogleAdsense from './components/GoogleAdsense';
import <PERSON>ript from 'next/script';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Free Games Online - Play the Best Free Games Instantly",
  description: "Free Games Online to play instantly — explore the best free games in action, puzzle, racing, and more. No download needed. Just click and play for free!",
  keywords: 'Free Games Online, free online games, play free online games, online games free',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
  alternates: {
    canonical: 'https://free-gamesonline.com',
  },
  openGraph: {
    type: 'website',
    url: 'https://free-gamesonline.com',
    title: 'Free Games Online - Play the Best Free Games Instantly',
    description: 'Free Games Online to play instantly — explore the best free games in action, puzzle, racing, and more. No download needed. Just click and play for free!',
    siteName: 'Free Games Online',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Games Online - Play the Best Free Games Instantly',
    description: 'Free Games Online to play instantly — explore the best free games in action, puzzle, racing, and more. No download needed. Just click and play for free!',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <Script
          defer
          data-domain="free-gamesonline.com"
          src="https://plausible.nancook.com/js/script.file-downloads.hash.outbound-links.pageview-props.tagged-events.js"
          strategy="beforeInteractive"
        />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <Script id="microsoft-clarity" strategy="afterInteractive">
          {`
            (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "rj1eir1mrh");
          `}
        </Script>
        <Script 
          src="https://platform-api.sharethis.com/js/sharethis.js#property=67668519d5bedc001907e655&product=inline-share-buttons"
          strategy="afterInteractive"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen flex flex-col`}
      >
        {/*<HeadScript />*/}
        <GoogleAnalytics />
        <GoogleAdsense />
        <Navbar />
        <main className="flex-grow">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
