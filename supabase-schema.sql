-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Categories table
CREATE TABLE free_games_online_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  image_url TEXT,
  game_count INTEGER DEFAULT 0,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create index for categories
CREATE INDEX idx_free_games_online_categories_slug ON free_games_online_categories(slug);

-- Tags table
CREATE TABLE free_games_online_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Create index for tags
CREATE INDEX idx_free_games_online_tags_slug ON free_games_online_tags(slug);

-- Games table
CREATE TABLE free_games_online_games (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  short_description TEXT NOT NULL,
  markdown_content TEXT,
  cover_image TEXT NOT NULL,
  screenshots JSONB DEFAULT '[]'::jsonb,
  embed_code TEXT, -- For embedding the game iframe
  rating DECIMAL(3,1) DEFAULT 0.0,
  play_count INTEGER DEFAULT 0,
  is_featured BOOLEAN DEFAULT false,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  category_id UUID REFERENCES free_games_online_categories(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create indexes for games
CREATE INDEX idx_free_games_online_games_slug ON free_games_online_games(slug);
CREATE INDEX idx_free_games_online_games_category ON free_games_online_games(category_id);
CREATE INDEX idx_free_games_online_games_rating ON free_games_online_games(rating);
CREATE INDEX idx_free_games_online_games_featured ON free_games_online_games(is_featured);
CREATE INDEX idx_free_games_online_games_status ON free_games_online_games(status);

-- Game-Tag relation (many-to-many)
CREATE TABLE free_games_online_game_tags (
  game_id UUID REFERENCES free_games_online_games(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES free_games_online_tags(id) ON DELETE CASCADE,
  PRIMARY KEY (game_id, tag_id)
);

-- Create index for game_tags
CREATE INDEX idx_free_games_online_game_tags ON free_games_online_game_tags(game_id, tag_id);

-- Function to update category game count
CREATE OR REPLACE FUNCTION update_free_games_online_category_game_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.category_id IS DISTINCT FROM NEW.category_id) THEN
    -- Increment count for new category
    IF NEW.category_id IS NOT NULL AND NEW.status = 'published' THEN
      UPDATE free_games_online_categories SET game_count = game_count + 1 WHERE id = NEW.category_id;
    END IF;
    
    -- Decrement count for old category if updating
    IF TG_OP = 'UPDATE' AND OLD.category_id IS NOT NULL AND OLD.status = 'published' THEN
      UPDATE free_games_online_categories SET game_count = game_count - 1 WHERE id = OLD.category_id;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrement count when deleting a game
    IF OLD.category_id IS NOT NULL AND OLD.status = 'published' THEN
      UPDATE free_games_online_categories SET game_count = game_count - 1 WHERE id = OLD.category_id;
    END IF;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to maintain category game counts
CREATE TRIGGER update_free_games_online_category_game_count_trigger
AFTER INSERT OR UPDATE OR DELETE ON free_games_online_games
FOR EACH ROW EXECUTE FUNCTION update_free_games_online_category_game_count();

-- Function to update timestamps on update
CREATE OR REPLACE FUNCTION update_free_games_online_modified_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add timestamps trigger to games table
CREATE TRIGGER update_free_games_online_games_timestamp
BEFORE UPDATE ON free_games_online_games
FOR EACH ROW EXECUTE FUNCTION update_free_games_online_modified_timestamp();

-- Add timestamps trigger to categories table
CREATE TRIGGER update_free_games_online_categories_timestamp
BEFORE UPDATE ON free_games_online_categories
FOR EACH ROW EXECUTE FUNCTION update_free_games_online_modified_timestamp(); 