'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';

export default function NewGamePage() {
  const router = useRouter();
  const [categories, setCategories] = useState<any[]>([]);
  const [tags, setTags] = useState<any[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [showNewTagForm, setShowNewTagForm] = useState(false);
  const [newTag, setNewTag] = useState({ name: '', slug: '' });
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    short_description: '',
    markdown_content: `# Game Title

## About the Game
Describe your game here.

## How to Play
1. First step
2. Second step
3. Third step

## Features
* Feature one
* Feature two
* Feature three`,
    cover_image: '',
    screenshots: '', // 以逗号分隔的URL列表
    embed_code: '', // 游戏嵌入链接
    rating: 4.5,
    category_id: '',
    is_featured: false,
    status: 'published'
  });
  
  // 获取所有分类和标签
  useEffect(() => {
    async function fetchData() {
      // 获取分类
      const { data: categoriesData } = await supabase
        .from('free_games_online_categories')
        .select('id, name, slug');
      
      if (categoriesData) setCategories(categoriesData);
      
      // 获取标签
      const { data: tagsData } = await supabase
        .from('free_games_online_tags')
        .select('id, name, slug');
      
      if (tagsData) setTags(tagsData);
    }
    
    fetchData();
  }, []);
  
  // 从标题生成slug
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');
  };
  
  // 表单输入变化处理
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // 自动生成slug
    if (name === 'title') {
      setFormData(prev => ({ ...prev, slug: generateSlug(value) }));
    }
  };
  
  // 复选框变化处理
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({ ...formData, [name]: checked });
  };
  
  // 标签选择变化处理
  const handleTagChange = (tagSlug: string) => {
    setSelectedTags(prev => {
      if (prev.includes(tagSlug)) {
        return prev.filter(t => t !== tagSlug);
      } else {
        return [...prev, tagSlug];
      }
    });
  };
  
  // 处理新标签名称变更，自动生成slug
  const handleNewTagNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    const slug = generateSlug(name);
    setNewTag({ name, slug });
  };
  
  // 处理新标签slug变更
  const handleNewTagSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewTag({ ...newTag, slug: e.target.value });
  };
  
  // 添加新标签
  const handleAddTag = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newTag.name || !newTag.slug) {
      setMessage('Tag name and slug are required');
      return;
    }
    
    try {
      // 检查标签是否已存在
      const { data: existingTag } = await supabase
        .from('free_games_online_tags')
        .select('id')
        .or(`name.eq.${newTag.name},slug.eq.${newTag.slug}`)
        .single();
        
      if (existingTag) {
        setMessage('A tag with this name or slug already exists');
        return;
      }
      
      // 插入新标签
      const { data: tagData, error: tagError } = await supabase
        .from('free_games_online_tags')
        .insert({
          name: newTag.name,
          slug: newTag.slug
        })
        .select('id, name, slug')
        .single();
        
      if (tagError) throw tagError;
      
      // 更新标签列表
      setTags([...tags, tagData]);
      
      // 选中新标签
      setSelectedTags([...selectedTags, tagData.slug]);
      
      // 重置表单
      setNewTag({ name: '', slug: '' });
      setShowNewTagForm(false);
      
      setMessage('Tag added successfully');
      setTimeout(() => setMessage(''), 3000);
    } catch (error: any) {
      console.error('Error adding tag:', error);
      setMessage(`Error: ${error.message}`);
    }
  };
  
  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');
    
    try {
      // 处理截图字符串为JSON数组
      const screenshots = formData.screenshots
        .split(',')
        .map(url => url.trim())
        .filter(url => url !== '');
      
      // 插入游戏
      const { data: gameData, error: gameError } = await supabase
        .from('free_games_online_games')
        .insert({
          title: formData.title,
          slug: formData.slug,
          short_description: formData.short_description,
          markdown_content: formData.markdown_content,
          cover_image: formData.cover_image,
          screenshots: screenshots,
          embed_code: formData.embed_code,
          rating: formData.rating,
          play_count: 0,
          is_featured: formData.is_featured,
          category_id: formData.category_id,
          status: formData.status
        })
        .select('id')
        .single();
      
      if (gameError) throw gameError;
      
      // 添加标签关联
      if (selectedTags.length > 0 && gameData) {
        const tagRelations = [];
        
        for (const tagSlug of selectedTags) {
          const { data: tagData } = await supabase
            .from('free_games_online_tags')
            .select('id')
            .eq('slug', tagSlug)
            .single();
            
          if (tagData) {
            tagRelations.push({
              game_id: gameData.id,
              tag_id: tagData.id
            });
          }
        }
        
        if (tagRelations.length > 0) {
          const { error: tagError } = await supabase
            .from('free_games_online_game_tags')
            .insert(tagRelations);
            
          if (tagError) console.error('Error adding tags:', tagError);
        }
      }
      
      setMessage('Game created successfully!');
      router.push(`/admin/games`);
    } catch (error: any) {
      console.error('Error creating game:', error);
      setMessage(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">Add New Game</h1>
      
      {message && (
        <div className={`p-4 mb-6 rounded-md ${message.includes('Error') ? 'bg-red-900/30 text-red-400' : 'bg-green-900/30 text-green-400'}`}>
          {message}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
        {/* 基本信息 */}
        <div className="mb-8 bg-gray-900/50 p-6 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-purple-400">Basic Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Title</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Slug</label>
              <input
                type="text"
                name="slug"
                value={formData.slug}
                onChange={handleInputChange}
                className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
                required
              />
            </div>
          </div>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-300 mb-1">Short Description</label>
            <textarea
              name="short_description"
              value={formData.short_description}
              onChange={handleInputChange}
              rows={2}
              className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
              required
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Cover Image URL</label>
              <input
                type="url"
                name="cover_image"
                value={formData.cover_image}
                onChange={handleInputChange}
                className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Rating</label>
              <input
                type="number"
                name="rating"
                value={formData.rating}
                onChange={handleInputChange}
                min="0"
                max="5"
                step="0.1"
                className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-300 mb-1">Screenshots URLs (comma-separated)</label>
            <textarea
              name="screenshots"
              value={formData.screenshots}
              onChange={handleInputChange}
              rows={2}
              className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-300 mb-1">Game Embed URL</label>
            <input
              type="text"
              name="embed_code"
              value={formData.embed_code}
              onChange={handleInputChange}
              placeholder="https://example.com/game/index.html"
              className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <p className="text-xs text-gray-500 mt-1">Enter the full URL to the game that will be embedded in an iframe</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Category</label>
              <select
                name="category_id"
                value={formData.category_id}
                onChange={handleInputChange}
                className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Status</label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
              </select>
            </div>
          </div>
          
          <div className="flex items-center mb-6">
            <input
              type="checkbox"
              id="is_featured"
              name="is_featured"
              checked={formData.is_featured}
              onChange={handleCheckboxChange}
              className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-700 rounded"
            />
            <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-300">
              Featured Game
            </label>
          </div>
        </div>
        
        {/* Markdown内容 */}
        <div className="mb-8 bg-gray-900/50 p-6 rounded-lg">
          <h2 className="text-xl font-bold mb-4 text-purple-400">Game Content (Markdown)</h2>
          <textarea
            name="markdown_content"
            value={formData.markdown_content}
            onChange={handleInputChange}
            rows={15}
            className="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-3 text-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono"
          />
        </div>
        
        {/* 标签 */}
        <div className="mb-8 bg-gray-900/50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-purple-400">Tags</h2>
            <button
              type="button"
              onClick={() => setShowNewTagForm(!showNewTagForm)}
              className="text-sm bg-purple-900/50 hover:bg-purple-800/50 text-purple-300 py-1 px-3 rounded"
            >
              {showNewTagForm ? 'Cancel' : 'Add New Tag'}
            </button>
          </div>
          
          {/* 新标签表单 */}
          {showNewTagForm && (
            <div className="mb-4 p-4 bg-gray-800/50 rounded-md">
              <h3 className="text-sm font-bold mb-2 text-gray-300">Add New Tag</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                <div>
                  <label className="block text-xs font-medium text-gray-400 mb-1">Name</label>
                  <input
                    type="text"
                    value={newTag.name}
                    onChange={handleNewTagNameChange}
                    className="w-full bg-gray-800 border border-gray-700 rounded-md py-1 px-2 text-gray-300 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-400 mb-1">Slug</label>
                  <input
                    type="text"
                    value={newTag.slug}
                    onChange={handleNewTagSlugChange}
                    className="w-full bg-gray-800 border border-gray-700 rounded-md py-1 px-2 text-gray-300 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500"
                  />
                </div>
              </div>
              <button
                type="button"
                onClick={handleAddTag}
                className="text-xs bg-green-900/50 hover:bg-green-800/50 text-green-300 py-1 px-3 rounded"
              >
                Add Tag
              </button>
            </div>
          )}
          
          {/* 标签列表 */}
          <div className="flex flex-wrap gap-2">
            {tags.map(tag => (
              <label
                key={tag.id}
                className={`px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${
                  selectedTags.includes(tag.slug)
                    ? 'bg-purple-600/70 text-white'
                    : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
                }`}
              >
                <input
                  type="checkbox"
                  value={tag.slug}
                  checked={selectedTags.includes(tag.slug)}
                  onChange={() => handleTagChange(tag.slug)}
                  className="sr-only"
                />
                {tag.name}
              </label>
            ))}
          </div>
        </div>
        
        {/* 提交按钮 */}
        <div className="flex justify-between">
          <button
            type="button"
            onClick={() => router.push('/admin/games')}
            className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-6 rounded-md"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className={`bg-purple-600 hover:bg-purple-700 text-white py-2 px-6 rounded-md ${
              loading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {loading ? 'Creating...' : 'Create Game'}
          </button>
        </div>
      </form>
    </div>
  );
} 