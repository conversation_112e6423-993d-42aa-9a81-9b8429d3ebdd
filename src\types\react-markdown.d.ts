declare module 'react-markdown' {
  import React from 'react';
  
  interface ReactMarkdownProps {
    children: string;
    remarkPlugins?: any[];
    rehypePlugins?: any[];
    components?: {
      [key: string]: React.ComponentType<any>;
    };
  }

  export default function ReactMarkdown(props: ReactMarkdownProps): JSX.Element;
}

declare module 'rehype-raw' {
  const rehypeRaw: any;
  export default rehypeRaw;
}

declare module 'remark-gfm' {
  const remarkGfm: any;
  export default remarkGfm;
} 