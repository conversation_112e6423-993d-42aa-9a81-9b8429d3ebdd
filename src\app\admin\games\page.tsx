'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { createSafeSupabaseClient } from '@/lib/supabase-client';

export default function GamesListPage() {
  const [games, setGames] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('');
  const supabase = createSafeSupabaseClient();

  // 获取所有游戏
  useEffect(() => {
    async function fetchGames() {
      setLoading(true);
      try {
        if (!supabase) {
          throw new Error('Database connection not available');
        }

        const { data, error } = await supabase
          .from('free_games_online_games')
          .select(`
            *,
            free_games_online_categories:category_id (id, name, slug)
          `)
          .order('created_at', { ascending: false });

        if (error) throw error;
        setGames(data || []);
      } catch (error: any) {
        console.error('Error fetching games:', error);
        setMessage(`Error: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    fetchGames();
  }, []);

  // 删除游戏
  const handleDelete = async (id: string, title: string) => {
    if (!confirm(`Are you sure you want to delete "${title}"? This action cannot be undone.`)) {
      return;
    }

    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      const { error } = await supabase
        .from('free_games_online_games')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // 更新列表
      setGames(games.filter(game => game.id !== id));
      setMessage('Game deleted successfully');

      // 清除成功消息
      setTimeout(() => setMessage(''), 3000);
    } catch (error: any) {
      console.error('Error deleting game:', error);
      setMessage(`Error: ${error.message}`);
    }
  };

  // 切换精选状态
  const toggleFeatured = async (id: string, isFeatured: boolean) => {
    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      const { error } = await supabase
        .from('free_games_online_games')
        .update({ is_featured: !isFeatured })
        .eq('id', id);

      if (error) throw error;

      // 更新列表
      setGames(games.map(game =>
        game.id === id ? { ...game, is_featured: !isFeatured } : game
      ));
    } catch (error: any) {
      console.error('Error updating featured status:', error);
      setMessage(`Error: ${error.message}`);
    }
  };

  // 更新游戏状态
  const updateStatus = async (id: string, status: string) => {
    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      const { error } = await supabase
        .from('free_games_online_games')
        .update({ status })
        .eq('id', id);

      if (error) throw error;

      // 更新列表
      setGames(games.map(game =>
        game.id === id ? { ...game, status } : game
      ));
    } catch (error: any) {
      console.error('Error updating status:', error);
      setMessage(`Error: ${error.message}`);
    }
  };

  // 状态标签样式
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-900/50 text-green-400';
      case 'draft':
        return 'bg-yellow-900/50 text-yellow-400';
      case 'archived':
        return 'bg-gray-900/50 text-gray-400';
      default:
        return 'bg-blue-900/50 text-blue-400';
    }
  };

  // 生成游戏链接
  const getGameUrl = (game: any) => {
    const categorySlug = game.free_games_online_categories?.slug || 'sprunki';
    return `/${categorySlug}/${game.slug}`;
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">All Games</h1>
        <Link
          href="/admin/games/new"
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg text-sm"
        >
          Add New Game
        </Link>
      </div>

      {message && (
        <div className={`p-4 mb-6 rounded-lg ${message.includes('Error') ? 'bg-red-700/50' : 'bg-green-700/50'}`}>
          {message}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center py-12">
          <svg className="animate-spin h-10 w-10 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      ) : games.length === 0 ? (
        <div className="text-center py-12 bg-gray-900/30 rounded-lg">
          <p className="text-gray-400">No games found. Get started by adding your first game.</p>
          <Link
            href="/admin/games/new"
            className="inline-block mt-4 px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg"
          >
            Add New Game
          </Link>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead className="bg-gray-900/50">
              <tr>
                <th className="px-4 py-3 text-left">Game</th>
                <th className="px-4 py-3 text-left">Category</th>
                <th className="px-4 py-3 text-center">Featured</th>
                <th className="px-4 py-3 text-center">Status</th>
                <th className="px-4 py-3 text-center">Rating</th>
                <th className="px-4 py-3 text-center">Plays</th>
                <th className="px-4 py-3 text-right">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-800">
              {games.map(game => (
                <tr key={game.id} className="hover:bg-gray-900/30">
                  <td className="px-4 py-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 relative flex-shrink-0">
                        <Image
                          src={game.cover_image || 'https://via.placeholder.com/100'}
                          alt={game.title}
                          fill
                          className="object-cover rounded-md"
                        />
                      </div>
                      <div>
                        <div className="font-medium">{game.title}</div>
                        <div className="text-xs text-gray-400">{game.slug}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-gray-300">
                    {game.free_games_online_categories?.name || 'No category'}
                  </td>
                  <td className="px-4 py-3 text-center">
                    <button
                      onClick={() => toggleFeatured(game.id, game.is_featured)}
                      className={`w-5 h-5 rounded-md ${game.is_featured ? 'bg-yellow-500' : 'bg-gray-700'}`}
                      title={game.is_featured ? 'Featured' : 'Not Featured'}
                    >
                      {game.is_featured && (
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="text-black">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.462a1 1 0 00.95-.69l1.07-3.292z" />
                        </svg>
                      )}
                    </button>
                  </td>
                  <td className="px-4 py-3 text-center">
                    <div className="flex justify-center">
                      <select
                        value={game.status}
                        onChange={(e) => updateStatus(game.id, e.target.value)}
                        className={`text-xs py-1 px-2 rounded-full ${getStatusBadgeClass(game.status)} bg-opacity-20 border-0 cursor-pointer`}
                      >
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                        <option value="archived">Archived</option>
                      </select>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-center">
                    <div className="flex items-center justify-center text-yellow-400">
                      <span>{game.rating.toFixed(1)}</span>
                      <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-center">
                    {new Intl.NumberFormat().format(game.play_count)}
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className="flex justify-end space-x-2">
                      <Link
                        href={`/admin/games/${game.id}`}
                        className="p-1 bg-purple-900/30 text-purple-400 hover:bg-purple-800/30 rounded"
                        title="Edit"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                      </Link>
                      <Link
                        href={getGameUrl(game)}
                        target="_blank"
                        className="p-1 bg-blue-900/30 text-blue-400 hover:bg-blue-800/30 rounded"
                        title="View"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                        </svg>
                      </Link>
                      <button
                        onClick={() => handleDelete(game.id, game.title)}
                        className="p-1 bg-red-900/30 text-red-400 hover:bg-red-800/30 rounded"
                        title="Delete"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
} 