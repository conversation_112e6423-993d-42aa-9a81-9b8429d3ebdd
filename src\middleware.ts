import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  // Check if accessing admin path
  const isAdminPath = req.nextUrl.pathname.startsWith('/admin');

  if (isAdminPath) {
    // Get session cookie
    const sessionCookie = req.cookies.get('admin_session')?.value;
    
    if (!sessionCookie) {
      // No session, redirect to login page
      const redirectUrl = new URL('/login', req.url);
      redirectUrl.searchParams.set('redirect', req.nextUrl.pathname);
      return NextResponse.redirect(redirectUrl);
    }
    
    try {
      // Parse session data
      const session = JSON.parse(sessionCookie);
      
      // Check if session is valid
      const isValid = session && 
                     session.is_admin === true && 
                     new Date(session.expires) > new Date();
      
      if (!isValid) {
        // Session invalid or expired, redirect to login page
        const redirectUrl = new URL('/login', req.url);
        redirectUrl.searchParams.set('redirect', req.nextUrl.pathname);
        return NextResponse.redirect(redirectUrl);
      }
    } catch (error) {
      // Error parsing session, redirect to login page
      const redirectUrl = new URL('/login', req.url);
      redirectUrl.searchParams.set('redirect', req.nextUrl.pathname);
      return NextResponse.redirect(redirectUrl);
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: ['/admin/:path*'],
}; 