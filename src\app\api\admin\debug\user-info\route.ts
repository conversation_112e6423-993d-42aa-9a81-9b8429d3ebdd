import { NextResponse } from 'next/server';
import { checkSupabaseAvailable } from '@/lib/supabase-server';

export const runtime = 'edge';

export async function GET(request: Request) {
  try {
    // 检查 Supabase 配置是否可用
    const supabase = checkSupabaseAvailable();
    
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');
    
    if (!username) {
      return NextResponse.json({
        error: 'Missing username parameter'
      }, { status: 400 });
    }
    
    // 查询用户信息
    const { data, error } = await supabase
      .from('free_games_online_categories_admin_users')
      .select('id, username, password_hash, created_at, last_login')
      .eq('username', username)
      .single();
    
    if (error) {
      return NextResponse.json({
        error: 'Database error',
        message: error.message
      }, { status: error.message.includes('No rows found') ? 404 : 500 });
    }
    
    return NextResponse.json({
      success: true,
      user: data
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Unexpected error',
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
} 