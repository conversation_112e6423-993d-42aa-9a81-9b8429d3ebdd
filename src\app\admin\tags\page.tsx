'use client';
import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

export default function TagsManagementPage() {
  const [tags, setTags] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('');
  const [newTag, setNewTag] = useState({ name: '', slug: '' });
  const [editingTag, setEditingTag] = useState<any>(null);
  
  // 获取所有标签
  useEffect(() => {
    async function fetchTags() {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('free_games_online_tags')
          .select('*')
          .order('name');
          
        if (error) throw error;
        setTags(data || []);
      } catch (error: any) {
        console.error('Error fetching tags:', error);
        setMessage(`Error: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }
    
    fetchTags();
  }, []);
  
  // 从名称生成slug
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');
  };
  
  // 处理标签名称变更
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    const slug = generateSlug(name);
    
    if (editingTag) {
      setEditingTag({ ...editingTag, name, slug });
    } else {
      setNewTag({ name, slug });
    }
  };
  
  // 处理标签slug变更
  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const slug = e.target.value;
    
    if (editingTag) {
      setEditingTag({ ...editingTag, slug });
    } else {
      setNewTag({ ...newTag, slug });
    }
  };
  
  // 添加新标签
  const handleAddTag = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTag.name || !newTag.slug) return;
    
    try {
      const { data, error } = await supabase
        .from('free_games_online_tags')
        .insert({
          name: newTag.name,
          slug: newTag.slug
        })
        .select();
        
      if (error) throw error;
      
      setTags([...tags, data[0]]);
      setNewTag({ name: '', slug: '' });
      setMessage('Tag added successfully');
      
      setTimeout(() => setMessage(''), 3000);
    } catch (error: any) {
      console.error('Error adding tag:', error);
      setMessage(`Error: ${error.message}`);
    }
  };
  
  // 开始编辑标签
  const startEdit = (tag: any) => {
    setEditingTag(tag);
    setNewTag({ name: '', slug: '' });
  };
  
  // 取消编辑
  const cancelEdit = () => {
    setEditingTag(null);
  };
  
  // 更新标签
  const handleUpdateTag = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingTag || !editingTag.name || !editingTag.slug) return;
    
    try {
      const { error } = await supabase
        .from('free_games_online_tags')
        .update({
          name: editingTag.name,
          slug: editingTag.slug
        })
        .eq('id', editingTag.id);
        
      if (error) throw error;
      
      setTags(tags.map(tag => tag.id === editingTag.id ? editingTag : tag));
      setEditingTag(null);
      setMessage('Tag updated successfully');
      
      setTimeout(() => setMessage(''), 3000);
    } catch (error: any) {
      console.error('Error updating tag:', error);
      setMessage(`Error: ${error.message}`);
    }
  };
  
  // 删除标签
  const handleDeleteTag = async (id: string, name: string) => {
    if (!confirm(`Are you sure you want to delete tag "${name}"? This may affect games using this tag.`)) {
      return;
    }
    
    try {
      const { error } = await supabase
        .from('free_games_online_tags')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
      
      setTags(tags.filter(tag => tag.id !== id));
      if (editingTag && editingTag.id === id) {
        setEditingTag(null);
      }
      
      setMessage('Tag deleted successfully');
      setTimeout(() => setMessage(''), 3000);
    } catch (error: any) {
      console.error('Error deleting tag:', error);
      setMessage(`Error: ${error.message}`);
    }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Manage Tags</h1>
      
      {message && (
        <div className={`p-4 mb-6 rounded-lg ${message.includes('Error') ? 'bg-red-700/50' : 'bg-green-700/50'}`}>
          {message}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <div className="bg-gray-900/30 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">
              {editingTag ? 'Edit Tag' : 'Add New Tag'}
            </h2>
            
            <form onSubmit={editingTag ? handleUpdateTag : handleAddTag} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <input 
                  type="text" 
                  value={editingTag ? editingTag.name : newTag.name} 
                  onChange={handleNameChange} 
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2"
                  placeholder="Tag Name"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Slug</label>
                <input 
                  type="text" 
                  value={editingTag ? editingTag.slug : newTag.slug} 
                  onChange={handleSlugChange} 
                  className="w-full bg-gray-800 border border-gray-700 rounded-lg px-4 py-2"
                  placeholder="tag-slug"
                  required
                />
                <p className="text-xs text-gray-400 mt-1">Used in URLs and filters</p>
              </div>
              
              <div className="flex space-x-3">
                <button
                  type="submit"
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg"
                >
                  {editingTag ? 'Update Tag' : 'Add Tag'}
                </button>
                
                {editingTag && (
                  <button
                    type="button"
                    onClick={cancelEdit}
                    className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg"
                  >
                    Cancel
                  </button>
                )}
              </div>
            </form>
          </div>
        </div>
        
        <div>
          <div className="bg-gray-900/30 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Tag List</h2>
            
            {loading ? (
              <div className="flex justify-center py-8">
                <svg className="animate-spin h-8 w-8 text-purple-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            ) : tags.length === 0 ? (
              <div className="text-center py-8 text-gray-400">
                No tags found. Add your first tag.
              </div>
            ) : (
              <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
                {tags.map(tag => (
                  <div 
                    key={tag.id} 
                    className={`flex items-center justify-between p-3 rounded-lg ${
                      editingTag && editingTag.id === tag.id ? 'bg-purple-900/30 border border-purple-800' : 'bg-gray-800/50'
                    }`}
                  >
                    <div>
                      <div className="font-medium">{tag.name}</div>
                      <div className="text-xs text-gray-400">{tag.slug}</div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => startEdit(tag)}
                        className="p-1 bg-blue-900/30 text-blue-400 hover:bg-blue-800/30 rounded"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleDeleteTag(tag.id, tag.name)}
                        className="p-1 bg-red-900/30 text-red-400 hover:bg-red-800/30 rounded"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 