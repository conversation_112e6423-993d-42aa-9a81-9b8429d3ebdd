/** @type {import('next').NextConfig} */
const nextConfig = {
  // output: 'export',  // 移除静态导出设置
  // distDir: '.vercel/output/static',  // 移除静态导出目录
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'image.free-gamesonline.com',
      },
      {
        protocol: 'https',
        hostname: '**.cloudfront.net',
      },
    ],
  },
  // 确保 Cloudflare Pages 可以处理静态导出
  trailingSlash: true,
  // 禁用严格模式以避免一些问题
  reactStrictMode: false,
  // 移除不支持的 appDir 选项
  experimental: {
    // 其他实验性选项可以放在这里
  }
}

module.exports = nextConfig 