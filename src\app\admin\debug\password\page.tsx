'use client';
import { useState } from 'react';
import bcrypt from 'bcryptjs';

export default function PasswordDebugPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [storedHash, setStoredHash] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  
  // 测试密码验证
  const testPassword = async () => {
    setLoading(true);
    try {
      // 本地验证
      const isValid = bcrypt.compareSync(password, storedHash);
      
      // 服务器验证
      const response = await fetch('/api/admin/debug/verify-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          password,
          storedHash,
        }),
      });
      
      const data = await response.json();
      
      setResult({
        clientVerification: isValid,
        serverVerification: data.isValid,
        details: data
      });
    } catch (error) {
      setResult({
        error: true,
        message: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setLoading(false);
    }
  };
  
  // 获取用户信息
  const getUserInfo = async () => {
    if (!username) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/debug/user-info?username=${encodeURIComponent(username)}`);
      const data = await response.json();
      
      if (data.user) {
        setStoredHash(data.user.password_hash || '');
      }
      
      setResult(data);
    } catch (error) {
      setResult({
        error: true,
        message: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Password Verification Debug</h1>
      
      <div className="bg-gray-800 p-4 rounded mb-6">
        <h2 className="text-xl font-bold mb-2">Get User Hash</h2>
        
        <div className="mb-4">
          <label className="block mb-1">Username</label>
          <div className="flex">
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="flex-1 bg-gray-900 border border-gray-700 rounded-l p-2"
            />
            <button
              onClick={getUserInfo}
              disabled={loading || !username}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-900 text-white px-4 py-2 rounded-r"
            >
              Get Info
            </button>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-800 p-4 rounded mb-6">
        <h2 className="text-xl font-bold mb-2">Verify Password</h2>
        
        <div className="mb-4">
          <label className="block mb-1">Password to Test</label>
          <input
            type="text"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full bg-gray-900 border border-gray-700 rounded p-2"
          />
        </div>
        
        <div className="mb-4">
          <label className="block mb-1">Stored Hash</label>
          <textarea
            value={storedHash}
            onChange={(e) => setStoredHash(e.target.value)}
            className="w-full bg-gray-900 border border-gray-700 rounded p-2 h-20"
          />
        </div>
        
        <button
          onClick={testPassword}
          disabled={loading || !password || !storedHash}
          className="bg-green-600 hover:bg-green-700 disabled:bg-green-900 text-white px-4 py-2 rounded"
        >
          Test Password
        </button>
      </div>
      
      {result && (
        <div className="bg-gray-800 p-4 rounded">
          <h2 className="text-xl font-bold mb-2">Result</h2>
          <pre className="bg-black/50 p-4 rounded overflow-auto max-h-60">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
} 