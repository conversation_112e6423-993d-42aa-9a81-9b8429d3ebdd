// 生成静态sitemap的脚本
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function generateSitemap() {
  try {
    console.log('Generating sitemap...');
    
    // 获取所有已发布的游戏及其分类信息
    const { data: games, error: gamesError } = await supabase
      .from('free_games_online_games')
      .select(`
        slug, 
        updated_at, 
        created_at,
        free_games_online_categories:category_id (
          slug
        )
      `)
      .eq('status', 'published')
      .order('created_at', { ascending: false });

    if (gamesError) {
      console.error('Error fetching games:', gamesError);
      return;
    }

    // 获取所有分类
    const { data: categories, error: categoriesError } = await supabase
      .from('free_games_online_categories')
      .select('slug, updated_at, created_at')
      .order('display_order', { ascending: true });

    if (categoriesError) {
      console.error('Error fetching categories:', categoriesError);
      return;
    }

    const baseUrl = 'https://free-gamesonline.com';
    const currentDate = new Date().toISOString().split('T')[0];

    // 构建sitemap XML
    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- 首页 -->
  <url>
    <loc>${baseUrl}/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  
  <!-- 分类页面 -->`;

    // 添加分类页面
    if (categories && categories.length > 0) {
      categories.forEach(category => {
        const lastmod = category.updated_at 
          ? new Date(category.updated_at).toISOString().split('T')[0]
          : new Date(category.created_at).toISOString().split('T')[0];
        
        sitemap += `
  <url>
    <loc>${baseUrl}/${category.slug}/</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;
      });
    }

    sitemap += `
  
  <!-- 游戏页面 -->`;

    // 添加游戏页面
    if (games && games.length > 0) {
      games.forEach(game => {
        const lastmod = game.updated_at 
          ? new Date(game.updated_at).toISOString().split('T')[0]
          : new Date(game.created_at).toISOString().split('T')[0];
        
        // 使用游戏的实际分类slug，如果没有则默认使用sprunki
        const categorySlug = game.free_games_online_categories?.slug || 'sprunki';
        
        sitemap += `
  <url>
    <loc>${baseUrl}/${categorySlug}/${game.slug}/</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`;
      });
    }

    sitemap += `
</urlset>`;

    // 写入文件
    const sitemapPath = path.join(__dirname, 'public', 'sitemap.xml');
    fs.writeFileSync(sitemapPath, sitemap);

    console.log(`✅ Sitemap generated successfully!`);
    console.log(`📊 Stats:`);
    console.log(`   - Categories: ${categories?.length || 0}`);
    console.log(`   - Games: ${games?.length || 0}`);
    console.log(`   - Total URLs: ${1 + (categories?.length || 0) + (games?.length || 0)}`);
    console.log(`📁 File: ${sitemapPath}`);

  } catch (error) {
    console.error('Error generating sitemap:', error);
  }
}

generateSitemap();