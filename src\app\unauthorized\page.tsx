import Link from 'next/link';

export default function UnauthorizedPage() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-purple-900/40 via-black to-black text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-red-400 via-purple-400 to-red-500 text-transparent bg-clip-text">
            Access Denied
          </h1>
          
          <div className="bg-red-900/20 border border-red-500/30 rounded-xl p-8 mb-8">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            
            <p className="text-xl text-gray-300 mb-4">
              You do not have permission to access this page
            </p>
            
            <p className="text-gray-400">
              This page is restricted to administrators only. If you believe this is an error, please contact the site administrator.
            </p>
          </div>
          
          <div className="flex justify-center gap-4">
            <Link 
              href="/"
              className="px-6 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
            >
              Return to Home
            </Link>
            
            <Link 
              href="/login"
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
            >
              Admin Login
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
} 