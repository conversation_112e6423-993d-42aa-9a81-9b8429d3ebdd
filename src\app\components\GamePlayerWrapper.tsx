'use client';

import React from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the GamePlayer component with client-side rendering
const GamePlayer = dynamic(() => import('./GamePlayer'), { ssr: false });

interface GamePlayerWrapperProps {
  embedUrl: string;
  coverImage: string;
  title: string;
}

const GamePlayerWrapper: React.FC<GamePlayerWrapperProps> = ({ embedUrl, coverImage, title }) => {
  return (
    <GamePlayer
      embedUrl={embedUrl}
      coverImage={coverImage}
      title={title}
    />
  );
};

export default GamePlayerWrapper; 