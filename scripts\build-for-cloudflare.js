/**
 * Optimized build script for Cloudflare Pages
 * This script solves issues with @cloudflare/next-on-pages on Windows
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting optimized build for Cloudflare Pages...');

try {
  // 1. Clean old build files
  console.log('🧹 Cleaning old build files...');
  if (fs.existsSync('./.vercel')) {
    fs.rmSync('./.vercel', { recursive: true, force: true });
  }
  
  // 2. Execute standard Next.js build
  console.log('🔨 Executing Next.js build...');
  // Add --no-lint option to skip ESLint checks
  execSync('next build --no-lint', { stdio: 'inherit' });
  
  // 3. Create directory structure required by Cloudflare
  console.log('📁 Creating Cloudflare Pages output directory structure...');
  fs.mkdirSync('./.vercel/output/static', { recursive: true });
  fs.mkdirSync('./.vercel/output/functions', { recursive: true });
  
  // 4. Copy static assets
  console.log('📋 Copying static assets...');
  
  // Copy public directory contents to static output
  const copyDirectory = (source, destination) => {
    if (!fs.existsSync(source)) return;
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }
    
    const files = fs.readdirSync(source);
    for (const file of files) {
      const sourcePath = path.join(source, file);
      const destPath = path.join(destination, file);
      
      const stat = fs.statSync(sourcePath);
      if (stat.isDirectory()) {
        copyDirectory(sourcePath, destPath);
      } else {
        fs.copyFileSync(sourcePath, destPath);
      }
    }
  };
  
  // Copy public directory
  copyDirectory('./public', './.vercel/output/static');
  
  // Copy .next/static directory
  copyDirectory('./.next/static', './.vercel/output/static/_next/static');
  
  // 5. Create basic configuration files
  console.log('📝 Creating configuration files...');
  
  // Create basic _headers file
  const headersContent = `# Default headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Static asset caching
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable

# Image caching
/images/*
  Cache-Control: public, max-age=86400
`;

  fs.writeFileSync('./.vercel/output/static/_headers', headersContent);
  
  // Create basic _routes.json file
  const routesContent = `{
  "version": 1,
  "include": ["/*"],
  "exclude": [
    "/_next/static/*",
    "/images/*",
    "/favicon.ico",
    "/robots.txt"
  ]
}`;

  fs.writeFileSync('./.vercel/output/static/_routes.json', routesContent);
  
  // Create basic config file
  const configContent = `{
  "version": 3,
  "routes": [
    {
      "src": "^/images/(.*)",
      "headers": { "cache-control": "public, max-age=86400" },
      "continue": true
    },
    {
      "src": "^/_next/static/(.*)",
      "headers": { "cache-control": "public, max-age=31536000, immutable" },
      "continue": true
    },
    { "handle": "filesystem" },
    { "src": "/(.*)", "dest": "/index.html" }
  ]
}`;

  fs.writeFileSync('./.vercel/output/config.json', configContent);
  
  // 6. Create functions files
  console.log('⚙️ Configuring Edge Functions...');
  
  const functionConfig = `{
  "runtime": "edge",
  "entrypoint": "index.js",
  "envVarsInUse": [
    "NEXT_PUBLIC_SUPABASE_URL",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY",
    "NEXT_PUBLIC_GA_ID",
    "NEXT_PUBLIC_ADSENSE_ID",
    "NEXT_PUBLIC_SITE_URL"
  ]
}`;

  // Create function config - using a safe approach, avoiding brackets
  // Create a common function config instead of separate ones for each route
  const defaultFuncDir = path.join('.vercel', 'output', 'functions', '_default.func');
  fs.mkdirSync(defaultFuncDir, { recursive: true });
  fs.writeFileSync(path.join(defaultFuncDir, '.vc-config.json'), functionConfig);
  
  // Create basic function file
  const defaultFunctionContent = `
  export default function middleware(request) {
    // This is a basic function script that will be handled by Cloudflare Pages
    return new Response("This page is handled by Cloudflare Pages", { status: 200 });
  }
  `;
  
  fs.writeFileSync(path.join(defaultFuncDir, 'index.js'), defaultFunctionContent);
  
  // Create function directories for main routes (using safe paths)
  const routes = [
    { path: 'sprunki', name: 'sprunki.func' },
    { path: 'tag', name: 'tag.func' },
    { path: 'api/admin/login', name: 'api_admin_login.func' },
    { path: 'api/admin/logout', name: 'api_admin_logout.func' },
    { path: 'api/admin/session', name: 'api_admin_session.func' },
    { path: 'api/admin/debug', name: 'api_admin_debug.func' }
  ];
  
  for (const route of routes) {
    try {
      // Use path.join to build paths
      const targetDir = path.join('.vercel', 'output', 'functions', route.name);
      
      // Ensure directory exists, including nested directories
      fs.mkdirSync(path.dirname(targetDir), { recursive: true });
      
      // Use path.join to write config
      const configPath = path.join(targetDir, '.vc-config.json');
      fs.writeFileSync(configPath, functionConfig);
      
      // Create basic function file
      const functionContent = `
      export default function middleware(request) {
        // This is a basic function script that will be handled by Cloudflare Pages
        return new Response("This page is handled by an Edge Function", { status: 200 });
      }
      `;
      
      const indexPath = path.join(targetDir, 'index.js');
      fs.writeFileSync(indexPath, functionContent);
    } catch (error) {
      console.error(`Failed to create function directory: ${route.path}`, error);
    }
  }
  
  // 7. Create a basic index.html as fallback
  console.log('📄 Creating basic index.html...');
  const indexHtmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Free Games Online</title>
  <meta name="description" content="Play the best free games online. No downloads required, play instantly in your browser!">
  <link rel="icon" href="/favicon.ico">
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      background: #000;
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    h1 {
      background: linear-gradient(to right, #ec4899, #8b5cf6);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }
    p {
      color: #d1d5db;
      max-width: 500px;
      margin-bottom: 2rem;
    }
    .button {
      background: linear-gradient(to right, #ec4899, #8b5cf6);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 9999px;
      text-decoration: none;
      font-weight: bold;
      transition: all 0.2s;
    }
    .button:hover {
      transform: scale(1.05);
    }
  </style>
</head>
<body>
  <h1>Free Games Online</h1>
  <p>Play hundreds of free games online - no downloads required!</p>
  <a class="button" href="/">Start Playing</a>
  <script>
    // Simple client-side route redirection
    if (window.location.pathname !== '/' && !window.location.pathname.includes('.')) {
      window.location.href = '/';
    }
  </script>
</body>
</html>`;

  fs.writeFileSync('./.vercel/output/static/index.html', indexHtmlContent);

  console.log('✅ Build complete! Output directory: .vercel/output/');
  console.log('🌐 Ready to deploy to Cloudflare Pages.');
} catch (error) {
  console.error('❌ Error during build process:', error);
  process.exit(1);
} 