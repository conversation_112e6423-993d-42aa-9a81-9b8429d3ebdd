import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// 安全的客户端组件 Supabase 客户端创建函数
export function createSafeSupabaseClient() {
  try {
    // 检查环境变量是否可用
    if (typeof window !== 'undefined' && 
        (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)) {
      console.warn('Supabase environment variables not available');
      return null;
    }
    
    return createClientComponentClient();
  } catch (error) {
    console.error('Failed to create Supabase client:', error);
    return null;
  }
}