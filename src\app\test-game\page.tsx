// 简化的测试页面 - 用于诊断5xx错误
export default function TestGamePage() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

  return (
    <div className="min-h-screen bg-black text-white p-8">
      <h1 className="text-3xl font-bold mb-6">🔍 环境变量测试页面</h1>
      
      <div className="bg-gray-900 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-bold mb-4">环境变量状态:</h2>
        <ul className="space-y-2">
          <li className={`flex items-center ${supabaseUrl ? 'text-green-400' : 'text-red-400'}`}>
            <span className="mr-2">{supabaseUrl ? '✅' : '❌'}</span>
            NEXT_PUBLIC_SUPABASE_URL: {supabaseUrl ? '已设置' : '未设置'}
          </li>
          <li className={`flex items-center ${supabaseKey ? 'text-green-400' : 'text-red-400'}`}>
            <span className="mr-2">{supabaseKey ? '✅' : '❌'}</span>
            NEXT_PUBLIC_SUPABASE_ANON_KEY: {supabaseKey ? '已设置' : '未设置'}
          </li>
          <li className={`flex items-center ${siteUrl ? 'text-green-400' : 'text-red-400'}`}>
            <span className="mr-2">{siteUrl ? '✅' : '❌'}</span>
            NEXT_PUBLIC_SITE_URL: {siteUrl || '未设置'}
          </li>
        </ul>
      </div>

      <div className="bg-gray-900 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-bold mb-4">运行时信息:</h2>
        <ul className="space-y-2">
          <li>当前时间: {new Date().toISOString()}</li>
          <li>Node.js 版本: {process.version}</li>
          <li>平台: {process.platform}</li>
        </ul>
      </div>

      <div className="bg-gray-900 p-6 rounded-lg">
        <h2 className="text-xl font-bold mb-4">测试说明:</h2>
        <p className="mb-2">如果你能看到这个页面，说明:</p>
        <ul className="list-disc list-inside space-y-1 text-gray-300">
          <li>Next.js 应用基本运行正常</li>
          <li>路由系统工作正常</li>
          <li>静态页面渲染正常</li>
        </ul>
        <p className="mt-4 text-yellow-400">
          如果环境变量显示"未设置"，这就是5xx错误的原因！
        </p>
      </div>
    </div>
  );
}