-- 添加新游戏到Sprunki分类
DO $$
DECLARE
  category_id UUID;
  game_id UUID;
BEGIN
  -- 获取Sprunki分类ID
  SELECT id INTO category_id FROM free_games_online_categories WHERE slug = 'sprunki';
  
  -- 如果分类不存在，则创建它
  IF category_id IS NULL THEN
    INSERT INTO free_games_online_categories (id, name, slug, description, display_order)
    VALUES (
      uuid_generate_v4(),
      'Sprunki Games',
      'sprunki',
      'Discover the best Sprunki Games to play instantly online for free!',
      1
    )
    RETURNING id INTO category_id;
  END IF;
  
  -- 插入新游戏
  INSERT INTO free_games_online_games
  (id, title, slug, short_description, markdown_content, cover_image, screenshots, rating, play_count, is_featured, category_id, status)
  VALUES
  (
    uuid_generate_v4(),  -- 自动生成UUID
    'Your New Game Title',  -- 游戏标题
    'your-new-game-slug',  -- URL友好的标识符
    'A short description of your game that appears in cards.',  -- 简短描述
    
    -- Markdown内容，可以包含游戏详情、玩法说明等
    '# Your New Game Title

## About the Game
Describe your game here with rich markdown formatting.

## How to Play
1. First step instruction
2. Second step instruction
3. Third step instruction

## Features
* Feature one
* Feature two
* Feature three',
    
    'https://image.free-gamesonline.com/your-game-cover.jpg',  -- 封面图片URL
    
    -- 游戏截图，JSON数组格式
    '["https://image.free-gamesonline.com/your-game-screenshot1.jpg", "https://image.free-gamesonline.com/your-game-screenshot2.jpg"]',
    
    4.5,  -- 游戏评分（0-5）
    0,    -- 初始播放次数
    false, -- 是否是特色游戏
    category_id,  -- 分类ID
    'published'   -- 状态：draft, published, archived
  )
  RETURNING id INTO game_id;
  
  -- 添加游戏标签关联（可选）
  -- 首先确保标签存在
  INSERT INTO free_games_online_tags (id, name, slug)
  VALUES 
    (uuid_generate_v4(), 'Your Tag 1', 'your-tag-1'),
    (uuid_generate_v4(), 'Your Tag 2', 'your-tag-2')
  ON CONFLICT (slug) DO NOTHING;  -- 如果标签已存在则忽略
  
  -- 添加游戏与标签的关联
  INSERT INTO free_games_online_game_tags (game_id, tag_id)
  SELECT game_id, t.id
  FROM free_games_online_tags t
  WHERE t.slug IN ('your-tag-1', 'your-tag-2');
  
END $$; 